# Salesforce DX specific
.sfdx/
.sf/
.localdevserver/

# LWC configuration files
**/jsconfig.json
**/.eslintrc.json

# LWC Jest
**/__tests__/**

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Dependency directories
node_modules/

# Eslint cache
.eslintcache

# MacOS system files
.DS_Store

# Windows system files
Thumbs.db
ehthumbs.db
[Dd]esktop.ini
$RECYCLE.BIN/

# VS Code settings
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json

# Local environment variables
.env

# Temporary deployment files
*-meta.xml.bak
*.sublime-project
*.sublime-workspace

# Backup directories
cleanup-backup/

# Temporary files
*.tmp
*.temp
*.swp
*~

# Compiled source
*.com
*.class
*.dll
*.exe
*.o
*.so

# Packages
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Conda environment
.conda/

# Python virtual environment
.venv/
