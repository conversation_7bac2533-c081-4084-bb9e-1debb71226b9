# ALXTimeLogger

A Salesforce application for tracking time spent on various Salesforce objects including standard objects (Account, <PERSON>, Contact, Opportunity) and custom objects (Potential, Assessment, Campaign, etc.).

## Features

- **Time Tracking**: Log time spent on different Salesforce objects
- **Automatic Object Detection**: Automatically detects the current record context
- **Multiple Object Support**: Works with standard and custom objects
- **Reporting**: Includes reports and dashboards for time analysis
- **Employee Dashboard**: View time logs by employee with detailed statistics

## Components

### Custom Objects

- **Time_Log__c**: Stores time tracking information

### Lightning Web Components

- **timeLogForm**: Create time logs from record pages
- **timeLogSummary**: Display recent time logs
- **timeLogStats**: Show statistics and charts
- **employeeTimeLogDashboard**: Comprehensive dashboard for time logs by employee

## Installation

### Prerequisites

- Salesforce DX CLI installed
- Authorized Salesforce org

### Deployment Steps

1. Clone this repository:
   ```
   git clone https://github.com/jisv-biziondigital/ALXTimeLogger.git
   cd ALXTimeLogger
   ```

2. Deploy to your Salesforce org:
   ```
   sf project deploy start -d force-app/main/default
   ```

3. Assign the Time_Log_User permission set to users:
   ```
   sf org assign permset -n Time_Log_User
   ```

## Usage

### Adding the Time Log Form to a Record Page

1. Navigate to the record page you want to modify in the Lightning App Builder
2. Search for "Time Log Form" in the Components panel
3. Drag and drop the component onto the page layout
4. Save and activate the page

### Creating a Time Log

1. Navigate to a record page where the Time Log Form is displayed
2. The form will automatically detect the object type and record ID
3. Enter the start time, end time, and description
4. Check the "Billable" checkbox if applicable
5. Click "Create Time Log"

### Viewing Time Log Statistics

1. Add the Time Log Stats component to a record page or app page
2. Configure the component properties as needed
3. View the statistics and charts for time logs

### Using the Employee Time Log Dashboard

1. Navigate to the Employee Time Log Dashboard tab
2. Select a time period filter (Current Week, Last Week, etc.)
3. Select an employee from the list on the left
4. View the detailed time log information and statistics on the right

## Development

### Project Structure

```
force-app/
├── main/
│   └── default/
│       ├── applications/       # Custom applications
│       ├── classes/            # Apex classes
│       ├── dashboards/         # Dashboards
│       ├── flexipages/         # Lightning pages
│       ├── flows/              # Flows
│       ├── lwc/                # Lightning Web Components
│       ├── objects/            # Custom objects
│       ├── permissionsets/     # Permission sets
│       ├── reports/            # Reports
│       ├── reportTypes/        # Report types
│       ├── staticresources/    # Static resources
│       └── tabs/               # Custom tabs
```

### Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit your changes: `git commit -m 'Add some feature'`
4. Push to the branch: `git push origin feature-name`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Documentation

For detailed documentation, see the [ALXTimeLogger Application Documentation](ALXTimeLogger%20Application%20Documentation.md) file.
