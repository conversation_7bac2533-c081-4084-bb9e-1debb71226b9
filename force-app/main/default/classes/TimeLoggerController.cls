/**
 * @description Controller class for Time Logger Lightning components
 * <AUTHOR>
 */
public with sharing class TimeLoggerController {

    /**
     * @description Starts a new time log
     * @param objectType The type of object being tracked
     * @param recordId The ID of the related record
     * @param description Optional description of the work
     * @return The newly created Time_Log__c record
     */
    @AuraEnabled
    public static Time_Log__c startTimeLog(String objectType, String recordId, String description) {
        try {
            if (String.isBlank(objectType) || String.isBlank(recordId)) {
                throw new TimeLoggerException('Object type and record ID are required');
            }
            return TimeLoggerService.startTimeLog(objectType, recordId, description);
        } catch (Exception e) {
            throw new AuraHandledException('Error starting time log: ' + e.getMessage());
        }
    }

    /**
     * @description Stops an active time log
     * @param timeLogId The ID of the Time Log to stop
     * @return The updated Time_Log__c record
     */
    @AuraEnabled
    public static Time_Log__c stopTimeLog(Id timeLogId) {
        try {
            return TimeLoggerService.stopTimeLog(timeLogId);
        } catch (Exception e) {
            throw new AuraHandledException('Error stopping time log: ' + e.getMessage());
        }
    }

    /**
     * @description Gets all active time logs for the current user
     * @return List of Time_Log__c records that are currently active
     */
    @AuraEnabled(cacheable=true)
    public static List<Time_Log__c> getActiveTimeLogs() {
        try {
            return TimeLoggerService.getActiveTimeLogs();
        } catch (Exception e) {
            throw new AuraHandledException('Error retrieving active time logs: ' + e.getMessage());
        }
    }

    /**
     * @description Gets recent time logs for the current user
     * @param limitCount The maximum number of records to return
     * @return List of recent Time_Log__c records
     */
    @AuraEnabled(cacheable=true)
    public static List<Time_Log__c> getRecentTimeLogs(Integer limitCount) {
        try {
            return TimeLoggerService.getRecentTimeLogs(limitCount);
        } catch (Exception e) {
            throw new AuraHandledException('Error retrieving recent time logs: ' + e.getMessage());
        }
    }

    /**
     * @description Creates a complete time log with start and end times
     * @param objectType The type of object being tracked
     * @param recordId The ID of the related record
     * @param startTime The start time of the work
     * @param endTime The end time of the work
     * @param description Optional description of the work
     * @param isBillable Whether the time is billable
     * @return The newly created Time_Log__c record
     */
    @AuraEnabled
    public static Time_Log__c createCompleteTimeLog(
        String objectType,
        String recordId,
        Datetime startTime,
        Datetime endTime,
        String description,
        Boolean isBillable
    ) {
        try {
            return TimeLoggerService.createCompleteTimeLog(
                objectType,
                recordId,
                startTime,
                endTime,
                description,
                isBillable
            );
        } catch (Exception e) {
            throw new AuraHandledException('Error creating time log: ' + e.getMessage());
        }
    }

    /**
     * @description Gets the object type name from a record ID
     * @param recordId The ID of the record
     * @return The object type name
     */
    @AuraEnabled(cacheable=true)
    public static String getObjectType(Id recordId) {
        try {
            return recordId.getSObjectType().getDescribe().getName();
        } catch (Exception e) {
            throw new AuraHandledException('Error determining object type: ' + e.getMessage());
        }
    }

    /**
     * @description Checks if the current user has access to the Time_Log__c object and its fields
     * @return Map of field names to their accessibility status
     */
    @AuraEnabled(cacheable=true)
    public static Map<String, Boolean> checkFieldAccessibility() {
        try {
            return TimeLoggerService.checkFieldAccessibility();
        } catch (Exception e) {
            throw new AuraHandledException('Error checking field accessibility: ' + e.getMessage());
        }
    }

    /**
     * @description Gets time log statistics for the current user
     * @return Map containing various time log statistics
     */
    @AuraEnabled(cacheable=true)
    public static Map<String, Object> getTimeLogStats() {
        try {
            return TimeLoggerService.getTimeLogStats();
        } catch (Exception e) {
            throw new AuraHandledException('Error retrieving time log statistics: ' + e.getMessage());
        }
    }

    /**
     * @description Gets a list of all users who have time logs
     * @return List of User records with time log summary information
     */
    @AuraEnabled(cacheable=true)
    public static List<Map<String, Object>> getAllTimeLogUsers() {
        try {
            return TimeLoggerService.getAllTimeLogUsers();
        } catch (Exception e) {
            throw new AuraHandledException('Error retrieving time log users: ' + e.getMessage());
        }
    }

    /**
     * @description Gets time log statistics for a specific user
     * @param userId The ID of the user to get statistics for
     * @param timeFilter The time filter to apply (e.g., 'THIS_MONTH', 'LAST_MONTH', 'THIS_YEAR', 'CUSTOM')
     * @param startDate The start date for custom time filter
     * @param endDate The end date for custom time filter
     * @return Map containing various time log statistics for the specified user
     */
    @AuraEnabled(cacheable=true)
    public static Map<String, Object> getUserTimeLogStats(
        Id userId,
        String timeFilter,
        Date startDate,
        Date endDate
    ) {
        try {
            return TimeLoggerService.getUserTimeLogStats(userId, timeFilter, startDate, endDate);
        } catch (Exception e) {
            throw new AuraHandledException('Error retrieving user time log statistics: ' + e.getMessage());
        }
    }

    /**
     * @description Gets time logs for a specific user with filtering options
     * @param userId The ID of the user to get time logs for
     * @param timeFilter The time filter to apply (e.g., 'THIS_MONTH', 'LAST_MONTH', 'THIS_YEAR', 'CUSTOM')
     * @param startDate The start date for custom time filter
     * @param endDate The end date for custom time filter
     * @param limitCount The maximum number of records to return
     * @return List of Time_Log__c records for the specified user
     */
    @AuraEnabled(cacheable=false)
    public static List<Time_Log__c> getUserTimeLogs(
        Id userId,
        String timeFilter,
        Date startDate,
        Date endDate,
        Integer limitCount
    ) {
        try {
            // Check if userId is null
            if (userId == null) {
                System.debug('WARNING: Null userId passed to getUserTimeLogs');
                return new List<Time_Log__c>();
            }

            System.debug('INFO: getUserTimeLogs called with userId: ' + userId +
                        ', timeFilter: ' + timeFilter +
                        ', startDate: ' + startDate +
                        ', endDate: ' + endDate +
                        ', limitCount: ' + limitCount);

            List<Time_Log__c> results = TimeLoggerService.getUserTimeLogs(userId, timeFilter, startDate, endDate, limitCount);

            System.debug('INFO: getUserTimeLogs returned ' + (results != null ? results.size() : 0) + ' records');
            if (results != null && !results.isEmpty()) {
                System.debug('INFO: First record: ' + results[0].Id + ', Object Type: ' + results[0].Object_Type__c);
            }

            return results;
        } catch (Exception e) {
            System.debug('ERROR in getUserTimeLogs: ' + e.getMessage() + ' - ' + e.getStackTraceString());
            throw new AuraHandledException('Error retrieving user time logs: ' + e.getMessage());
        }
    }
}
