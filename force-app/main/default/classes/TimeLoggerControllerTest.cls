/**
 * @description Test class for TimeLoggerController
 * <AUTHOR>
 */
@isTest
private class TimeLoggerControllerTest {

    @testSetup
    static void setupTestData() {
        // Use the current user for testing
        User testUser = UserInfo.getUserId() != null ?
            [SELECT Id, FirstName, LastName FROM User WHERE Id = :UserInfo.getUserId()] :
            [SELECT Id, FirstName, LastName FROM User LIMIT 1];

        // Create a second user reference (same as first for testing)
        User testUser2 = testUser;

        // Create test account
        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;

        // Create test opportunity
        Opportunity testOpportunity = new Opportunity(
            Name = 'Test Opportunity',
            AccountId = testAccount.Id,
            StageName = 'Prospecting',
            CloseDate = Date.today().addDays(30)
        );
        insert testOpportunity;

        // Create test case
        Case testCase = new Case(
            Subject = 'Test Case',
            Status = 'New',
            Origin = 'Web'
        );
        insert testCase;

        System.runAs(testUser) {
            // Create a completed time log
            Time_Log__c completedLog = new Time_Log__c(
                Object_Type__c = 'Account',
                Related_Object__c = testAccount.Id,
                Account__c = testAccount.Id,
                Description__c = 'Test completed log',
                Start_Time__c = DateTime.now().addHours(-2),
                End_Time__c = DateTime.now().addHours(-1),
                Billable__c = true,
                User__c = testUser.Id
            );
            insert completedLog;

            // Create an active time log
            Time_Log__c activeLog = new Time_Log__c(
                Object_Type__c = 'Case',
                Related_Object__c = testCase.Id,
                Case__c = testCase.Id,
                Description__c = 'Test active log',
                Start_Time__c = DateTime.now().addMinutes(-30),
                User__c = testUser.Id
            );
            insert activeLog;
        }

        System.runAs(testUser2) {
            // Create time logs for second user
            Time_Log__c log1 = new Time_Log__c(
                Object_Type__c = 'Opportunity',
                Related_Object__c = testOpportunity.Id,
                Opportunity__c = testOpportunity.Id,
                Description__c = 'Test log for user 2',
                Start_Time__c = DateTime.now().addDays(-5),
                End_Time__c = DateTime.now().addDays(-5).addHours(3),
                Billable__c = true,
                User__c = testUser2.Id
            );

            Time_Log__c log2 = new Time_Log__c(
                Object_Type__c = 'Account',
                Related_Object__c = testAccount.Id,
                Account__c = testAccount.Id,
                Description__c = 'Another test log for user 2',
                Start_Time__c = DateTime.now().addDays(-2),
                End_Time__c = DateTime.now().addDays(-2).addHours(2),
                Billable__c = false,
                User__c = testUser2.Id
            );

            insert new List<Time_Log__c>{log1, log2};
        }
    }

    @isTest
    static void testStartTimeLog() {
        User testUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];
        Case testCase = [SELECT Id FROM Case WHERE Subject = 'Test Case' LIMIT 1];

        System.runAs(testUser) {
            Test.startTest();
            Time_Log__c timeLog = TimeLoggerController.startTimeLog('Case', testCase.Id, 'Test description');
            Test.stopTest();

            System.assertNotEquals(null, timeLog.Id, 'Time log should be created');
            System.assertEquals('Case', timeLog.Object_Type__c, 'Object type should be Case');
            System.assertEquals(testCase.Id, timeLog.Related_Object__c, 'Related record ID should match');
            System.assertEquals('Test description', timeLog.Description__c, 'Description should match');
            System.assertEquals(testUser.Id, timeLog.User__c, 'User ID should match current user');
            System.assertNotEquals(null, timeLog.Start_Time__c, 'Start time should be set');
            System.assertEquals(null, timeLog.End_Time__c, 'End time should be null');
        }
    }

    @isTest
    static void testStopTimeLog() {
        User testUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];
        // Get active time logs (where End_Time__c is null)
        Time_Log__c activeLog = [SELECT Id FROM Time_Log__c WHERE End_Time__c = null LIMIT 1];

        System.runAs(testUser) {
            Test.startTest();
            Time_Log__c stoppedLog = TimeLoggerController.stopTimeLog(activeLog.Id);
            Test.stopTest();

            System.assertNotEquals(null, stoppedLog.End_Time__c, 'End time should be set');
        }
    }

    @isTest
    static void testGetActiveTimeLogs() {
        User testUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];

        System.runAs(testUser) {
            Test.startTest();
            List<Time_Log__c> activeLogs = TimeLoggerController.getActiveTimeLogs();
            Test.stopTest();

            System.assertNotEquals(0, activeLogs.size(), 'Should have at least one active time log');
            if (activeLogs.size() > 0) {
                System.assertNotEquals(null, activeLogs[0].Description__c, 'Description should not be null');
            }
        }
    }

    @isTest
    static void testGetRecentTimeLogs() {
        User testUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];

        System.runAs(testUser) {
            Test.startTest();
            List<Time_Log__c> recentLogs = TimeLoggerController.getRecentTimeLogs(5);
            Test.stopTest();

            System.assertNotEquals(0, recentLogs.size(), 'Should have at least one time log');
        }
    }

    @isTest
    static void testCreateCompleteTimeLog() {
        User testUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Account' LIMIT 1];

        System.runAs(testUser) {
            Datetime startTime = Datetime.now().addHours(-3);
            Datetime endTime = Datetime.now().addHours(-2);

            Test.startTest();
            Time_Log__c timeLog = TimeLoggerController.createCompleteTimeLog(
                'Account',
                testAccount.Id,
                startTime,
                endTime,
                'Test complete log',
                true
            );
            Test.stopTest();

            System.assertNotEquals(null, timeLog.Id, 'Time log should be created');
            System.assertEquals('Account', timeLog.Object_Type__c, 'Object type should be Account');
            System.assertEquals(testAccount.Id, timeLog.Related_Object__c, 'Related record ID should match');
            System.assertEquals('Test complete log', timeLog.Description__c, 'Description should match');
            System.assertEquals(testUser.Id, timeLog.User__c, 'User ID should match current user');
            System.assertEquals(startTime, timeLog.Start_Time__c, 'Start time should match');
            System.assertEquals(endTime, timeLog.End_Time__c, 'End time should match');
            System.assertEquals(true, timeLog.Billable__c, 'Billable should be true');
        }
    }

    @isTest
    static void testGetObjectType() {
        User testUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Account' LIMIT 1];

        System.runAs(testUser) {
            Test.startTest();
            String objectType = TimeLoggerController.getObjectType(testAccount.Id);
            Test.stopTest();

            System.assertEquals('Account', objectType, 'Object type should be Account');
        }
    }

    @isTest
    static void testCheckFieldAccessibility() {
        User testUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];

        System.runAs(testUser) {
            Test.startTest();
            Map<String, Boolean> fieldAccessibility = TimeLoggerController.checkFieldAccessibility();
            Test.stopTest();

            System.assertNotEquals(null, fieldAccessibility, 'Field accessibility map should not be null');
            // Don't assert specific values since permissions may vary in different orgs
        }
    }

    @isTest
    static void testGetTimeLogStats() {
        User testUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];

        System.runAs(testUser) {
            Test.startTest();
            Map<String, Object> stats = TimeLoggerController.getTimeLogStats();
            Test.stopTest();

            System.assertNotEquals(null, stats, 'Stats map should not be null');
            System.assertNotEquals(null, stats.get('totalHours'), 'Total hours should not be null');
            System.assertNotEquals(null, stats.get('billableHours'), 'Billable hours should not be null');
            System.assertNotEquals(null, stats.get('byObjectType'), 'By object type should not be null');
            System.assertNotEquals(null, stats.get('byWeek'), 'By week should not be null');
        }
    }

    @isTest
    static void testExceptionHandling() {
        User testUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];

        System.runAs(testUser) {
            Test.startTest();
            try {
                // Pass null ID to trigger exception
                TimeLoggerController.stopTimeLog(null);
                System.assert(false, 'Exception should have been thrown');
            } catch (Exception e) {
                // Any exception is acceptable for this test
                System.assert(true, 'Exception was thrown as expected');
            }
            Test.stopTest();
        }
    }

    @isTest
    static void testGetAllTimeLogUsers() {
        Test.startTest();
        List<Map<String, Object>> users = TimeLoggerController.getAllTimeLogUsers();
        Test.stopTest();

        // Verify results - we're only using one user now
        System.assertNotEquals(0, users.size(), 'Should return at least one user');

        // Verify user data
        for (Map<String, Object> user : users) {
            System.assertNotEquals(null, user.get('userId'), 'User ID should not be null');
            System.assertNotEquals(null, user.get('userName'), 'User name should not be null');
            System.assertNotEquals(null, user.get('totalHours'), 'Total hours should not be null');
            System.assertNotEquals(null, user.get('logCount'), 'Log count should not be null');
        }
    }

    @isTest
    static void testGetUserTimeLogStats() {
        // Get a test user
        User testUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];

        // Execute the method with default parameters
        Test.startTest();
        Map<String, Object> stats = TimeLoggerController.getUserTimeLogStats(testUser.Id, null, null, null);
        Test.stopTest();

        // Verify results
        System.assertNotEquals(null, stats, 'Stats should not be null');
        System.assertNotEquals(null, stats.get('totalHours'), 'Total hours should not be null');
        System.assertNotEquals(null, stats.get('billableHours'), 'Billable hours should not be null');

        // Verify object type data
        List<Map<String, Object>> byObjectType = (List<Map<String, Object>>)stats.get('byObjectType');
        System.assertNotEquals(null, byObjectType, 'Object type data should not be null');

        // Verify monthly data
        List<Map<String, Object>> byMonth = (List<Map<String, Object>>)stats.get('byMonth');
        System.assertNotEquals(null, byMonth, 'Monthly data should not be null');
        System.assertNotEquals(0, byMonth.size(), 'Should have months of data');

        // Test with different time filters
        Map<String, Object> thisMonthStats = TimeLoggerController.getUserTimeLogStats(
            testUser.Id, 'THIS_MONTH', null, null
        );
        System.assertNotEquals(null, thisMonthStats, 'This month stats should not be null');

        Map<String, Object> lastMonthStats = TimeLoggerController.getUserTimeLogStats(
            testUser.Id, 'LAST_MONTH', null, null
        );
        System.assertNotEquals(null, lastMonthStats, 'Last month stats should not be null');

        Map<String, Object> thisYearStats = TimeLoggerController.getUserTimeLogStats(
            testUser.Id, 'THIS_YEAR', null, null
        );
        System.assertNotEquals(null, thisYearStats, 'This year stats should not be null');

        Date startDate = Date.today().addDays(-30);
        Date endDate = Date.today();
        Map<String, Object> customStats = TimeLoggerController.getUserTimeLogStats(
            testUser.Id, 'CUSTOM', startDate, endDate
        );
        System.assertNotEquals(null, customStats, 'Custom stats should not be null');

        Map<String, Object> allStats = TimeLoggerController.getUserTimeLogStats(
            testUser.Id, 'ALL', null, null
        );
        System.assertNotEquals(null, allStats, 'All stats should not be null');
    }

    @isTest
    static void testGetUserTimeLogs() {
        // Get a test user
        User testUser = [SELECT Id FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];

        // Test with CURRENT_WEEK filter
        Test.startTest();
        List<Time_Log__c> currentWeekLogs = TimeLoggerController.getUserTimeLogs(
            testUser.Id, 'CURRENT_WEEK', null, null, 10
        );

        // Test with LAST_WEEK filter
        List<Time_Log__c> lastWeekLogs = TimeLoggerController.getUserTimeLogs(
            testUser.Id, 'LAST_WEEK', null, null, 10
        );

        // Test with THIS_MONTH filter
        List<Time_Log__c> thisMonthLogs = TimeLoggerController.getUserTimeLogs(
            testUser.Id, 'THIS_MONTH', null, null, 10
        );

        // Test with LAST_MONTH filter
        List<Time_Log__c> lastMonthLogs = TimeLoggerController.getUserTimeLogs(
            testUser.Id, 'LAST_MONTH', null, null, 10
        );

        // Test with THIS_YEAR filter
        List<Time_Log__c> thisYearLogs = TimeLoggerController.getUserTimeLogs(
            testUser.Id, 'THIS_YEAR', null, null, 10
        );

        // Test with CUSTOM filter
        Date startDate = Date.today().addDays(-30);
        Date endDate = Date.today();
        List<Time_Log__c> customLogs = TimeLoggerController.getUserTimeLogs(
            testUser.Id, 'CUSTOM', startDate, endDate, 10
        );

        // Test with ALL filter
        List<Time_Log__c> allLogs = TimeLoggerController.getUserTimeLogs(
            testUser.Id, 'ALL', null, null, 10
        );
        Test.stopTest();

        // Verify results
        System.assertNotEquals(null, currentWeekLogs, 'Current week logs should not be null');
        System.assertNotEquals(null, lastWeekLogs, 'Last week logs should not be null');
        System.assertNotEquals(null, thisMonthLogs, 'This month logs should not be null');
        System.assertNotEquals(null, lastMonthLogs, 'Last month logs should not be null');
        System.assertNotEquals(null, thisYearLogs, 'This year logs should not be null');
        System.assertNotEquals(null, customLogs, 'Custom logs should not be null');
        System.assertNotEquals(null, allLogs, 'All logs should not be null');

        // Verify we have logs
        System.assertNotEquals(0, allLogs.size(), 'Should have logs for this user');
    }
}
