/**
 * @description Service class for Time Log functionality
 * <AUTHOR>
 */
public with sharing class TimeLoggerService {

    /**
     * @description Checks if the current user has access to the Time_Log__c object and its fields
     * @return Map of field names to their accessibility status
     */
    public static Map<String, Boolean> checkFieldAccessibility() {
        Map<String, Boolean> fieldAccessibility = new Map<String, Boolean>();

        // Check object accessibility
        Schema.DescribeSObjectResult objDescribe = Schema.SObjectType.Time_Log__c;
        Boolean isAccessible = objDescribe.isAccessible();
        Boolean isCreateable = objDescribe.isCreateable();
        Boolean isUpdateable = objDescribe.isUpdateable();

        fieldAccessibility.put('objectAccessible', isAccessible);
        fieldAccessibility.put('objectCreateable', isCreateable);
        fieldAccessibility.put('objectUpdateable', isUpdateable);

        // Check field accessibility
        Map<String, Schema.SObjectField> fieldMap = objDescribe.fields.getMap();

        for (String fieldName : fieldMap.keySet()) {
            Schema.DescribeFieldResult fieldDescribe = fieldMap.get(fieldName).getDescribe();
            fieldAccessibility.put(fieldName + '_accessible', fieldDescribe.isAccessible());
            fieldAccessibility.put(fieldName + '_createable', fieldDescribe.isCreateable());
            fieldAccessibility.put(fieldName + '_updateable', fieldDescribe.isUpdateable());
        }

        return fieldAccessibility;
    }

    /**
     * @description Gets time log statistics for the current user
     * @return Map containing various time log statistics
     */
    public static Map<String, Object> getTimeLogStats() {
        Map<String, Object> stats = new Map<String, Object>();
        Id currentUserId = UserInfo.getUserId();

        // Get total hours and billable hours
        AggregateResult[] totalResults = [
            SELECT SUM(Duration_Hours__c) totalHours
            FROM Time_Log__c
            WHERE User__c = :currentUserId
            AND End_Time__c != null
        ];

        AggregateResult[] billableResults = [
            SELECT SUM(Duration_Hours__c) billableHours
            FROM Time_Log__c
            WHERE User__c = :currentUserId
            AND End_Time__c != null
            AND Billable__c = true
        ];

        Decimal totalHours = (Decimal)totalResults[0].get('totalHours');
        Decimal billableHours = (Decimal)billableResults[0].get('billableHours');

        stats.put('totalHours', totalHours != null ? totalHours.setScale(2) : 0);
        stats.put('billableHours', billableHours != null ? billableHours.setScale(2) : 0);

        // Get hours by object type
        AggregateResult[] objectTypeResults = [
            SELECT Object_Type__c objectType, SUM(Duration_Hours__c) hours
            FROM Time_Log__c
            WHERE User__c = :currentUserId
            AND End_Time__c != null
            GROUP BY Object_Type__c
            ORDER BY SUM(Duration_Hours__c) DESC
        ];

        List<Map<String, Object>> byObjectType = new List<Map<String, Object>>();
        for (AggregateResult ar : objectTypeResults) {
            String objectType = (String)ar.get('objectType');
            Decimal hours = (Decimal)ar.get('hours');

            if (objectType != null && hours != null) {
                Map<String, Object> item = new Map<String, Object>{
                    'objectType' => objectType,
                    'hours' => hours.setScale(2)
                };
                byObjectType.add(item);
            }
        }

        stats.put('byObjectType', byObjectType);

        // Get weekly data (last 4 weeks)
        List<Map<String, Object>> byWeek = new List<Map<String, Object>>();
        Date today = Date.today();

        for (Integer i = 3; i >= 0; i--) {
            Date weekStart = today.toStartOfWeek().addDays(-7 * i);
            Date weekEnd = weekStart.addDays(6);

            AggregateResult[] weeklyBillableResults = [
                SELECT SUM(Duration_Hours__c) hours
                FROM Time_Log__c
                WHERE User__c = :currentUserId
                AND End_Time__c != null
                AND Billable__c = true
                AND Start_Time__c >= :Datetime.newInstance(weekStart, Time.newInstance(0, 0, 0, 0))
                AND Start_Time__c <= :Datetime.newInstance(weekEnd, Time.newInstance(23, 59, 59, 999))
            ];

            AggregateResult[] weeklyNonBillableResults = [
                SELECT SUM(Duration_Hours__c) hours
                FROM Time_Log__c
                WHERE User__c = :currentUserId
                AND End_Time__c != null
                AND Billable__c = false
                AND Start_Time__c >= :Datetime.newInstance(weekStart, Time.newInstance(0, 0, 0, 0))
                AND Start_Time__c <= :Datetime.newInstance(weekEnd, Time.newInstance(23, 59, 59, 999))
            ];

            Decimal billableHoursWeek = (Decimal)weeklyBillableResults[0].get('hours');
            Decimal nonBillableHoursWeek = (Decimal)weeklyNonBillableResults[0].get('hours');

            Map<String, Object> weekData = new Map<String, Object>{
                'weekLabel' => 'Week ' + weekStart.month() + '/' + weekStart.day(),
                'weekStart' => weekStart,
                'weekEnd' => weekEnd,
                'billableHours' => billableHoursWeek != null ? billableHoursWeek.setScale(2) : 0,
                'nonBillableHours' => nonBillableHoursWeek != null ? nonBillableHoursWeek.setScale(2) : 0
            };

            byWeek.add(weekData);
        }

        stats.put('byWeek', byWeek);

        return stats;
    }

    /**
     * @description Creates a new Time Log record with the start time set to now
     * @param objectType The type of object being tracked
     * @param relatedRecordId The ID of the related record
     * @param description Optional description of the work
     * @return The newly created Time_Log__c record
     */
    public static Time_Log__c startTimeLog(String objectType, String relatedRecordId, String description) {
        Time_Log__c timeLog = new Time_Log__c(
            Object_Type__c = objectType,
            Related_Object__c = relatedRecordId,
            Description__c = description,
            Start_Time__c = Datetime.now(),
            User__c = UserInfo.getUserId()
        );

        // Set the specific lookup field based on object type
        if (objectType == 'Account' && relatedRecordId != null) {
            timeLog.Account__c = relatedRecordId;
        } else if (objectType == 'Assessment__c' && relatedRecordId != null) {
            // Store in Related_Object__c field
            timeLog.Related_Object__c = relatedRecordId;
        } else if (objectType == 'Campaign' && relatedRecordId != null) {
            // Store in Related_Object__c field
            timeLog.Related_Object__c = relatedRecordId;
        } else if (objectType == 'Case' && relatedRecordId != null) {
            timeLog.Case__c = relatedRecordId;
        } else if (objectType == 'Case_Activity__c' && relatedRecordId != null) {
            // Store in Related_Object__c field
            timeLog.Related_Object__c = relatedRecordId;
        } else if (objectType == 'Contact' && relatedRecordId != null) {
            timeLog.Contact__c = relatedRecordId;
            // Store in Related_Object__c field
            timeLog.Related_Object__c = relatedRecordId;
        } else if (objectType == 'Opportunity' && relatedRecordId != null) {
            timeLog.Opportunity__c = relatedRecordId;
        } else if (objectType == 'Potential__c' && relatedRecordId != null) {
            // Store in Related_Object__c field
            timeLog.Related_Object__c = relatedRecordId;
        } else if (objectType == 'Recognition__c' && relatedRecordId != null) {
            // Store in Related_Object__c field
            timeLog.Related_Object__c = relatedRecordId;
        }

        insert timeLog;
        return timeLog;
    }

    /**
     * @description Stops an active time log by setting the end time to now
     * @param timeLogId The ID of the Time Log to stop
     * @return The updated Time_Log__c record
     */
    public static Time_Log__c stopTimeLog(Id timeLogId) {
        Time_Log__c timeLog = [SELECT Id, Start_Time__c, End_Time__c FROM Time_Log__c WHERE Id = :timeLogId LIMIT 1];

        if (timeLog.End_Time__c == null) {
            timeLog.End_Time__c = Datetime.now();
            update timeLog;
        }

        return timeLog;
    }

    /**
     * @description Gets all active time logs for the current user
     * @return List of Time_Log__c records that are currently active (no end time)
     */
    public static List<Time_Log__c> getActiveTimeLogs() {
        return [
            SELECT Id, Object_Type__c, Related_Object__c, Start_Time__c, Description__c,
                   Account__c, Case__c, Contact__c, Opportunity__c
            FROM Time_Log__c
            WHERE User__c = :UserInfo.getUserId()
            AND End_Time__c = null
            ORDER BY Start_Time__c DESC
        ];
    }

    /**
     * @description Gets recent time logs for the current user
     * @param limit The maximum number of records to return
     * @return List of recent Time_Log__c records
     */
    public static List<Time_Log__c> getRecentTimeLogs(Integer limitCount) {
        return [
            SELECT Id, Object_Type__c, Related_Object__c, Start_Time__c, End_Time__c,
                   Duration_Hours__c, Description__c, Billable__c,
                   Account__c, Case__c, Contact__c, Opportunity__c
            FROM Time_Log__c
            WHERE User__c = :UserInfo.getUserId()
            ORDER BY Start_Time__c DESC
            LIMIT :limitCount
        ];
    }

    /**
     * @description Creates a complete time log with start and end times
     * @param objectType The type of object being tracked
     * @param relatedRecordId The ID of the related record
     * @param startTime The start time of the work
     * @param endTime The end time of the work
     * @param description Optional description of the work
     * @param isBillable Whether the time is billable
     * @return The newly created Time_Log__c record
     */
    public static Time_Log__c createCompleteTimeLog(
        String objectType,
        String relatedRecordId,
        Datetime startTime,
        Datetime endTime,
        String description,
        Boolean isBillable
    ) {
        Time_Log__c timeLog = new Time_Log__c(
            Object_Type__c = objectType,
            Related_Object__c = relatedRecordId,
            Description__c = description,
            Start_Time__c = startTime,
            End_Time__c = endTime,
            Billable__c = isBillable,
            User__c = UserInfo.getUserId()
        );

        // Set the specific lookup field based on object type
        if (objectType == 'Account' && relatedRecordId != null) {
            timeLog.Account__c = relatedRecordId;
        } else if (objectType == 'Assessment__c' && relatedRecordId != null) {
            // Store in Related_Object__c field
            timeLog.Related_Object__c = relatedRecordId;
        } else if (objectType == 'Campaign' && relatedRecordId != null) {
            // Store in Related_Object__c field
            timeLog.Related_Object__c = relatedRecordId;
        } else if (objectType == 'Case' && relatedRecordId != null) {
            timeLog.Case__c = relatedRecordId;
        } else if (objectType == 'Case_Activity__c' && relatedRecordId != null) {
            // Store in Related_Object__c field
            timeLog.Related_Object__c = relatedRecordId;
        } else if (objectType == 'Contact' && relatedRecordId != null) {
            timeLog.Contact__c = relatedRecordId;
            // Store in Related_Object__c field
            timeLog.Related_Object__c = relatedRecordId;
        } else if (objectType == 'Opportunity' && relatedRecordId != null) {
            timeLog.Opportunity__c = relatedRecordId;
        } else if (objectType == 'Potential__c' && relatedRecordId != null) {
            // Store in Related_Object__c field
            timeLog.Related_Object__c = relatedRecordId;
        } else if (objectType == 'Recognition__c' && relatedRecordId != null) {
            // Store in Related_Object__c field
            timeLog.Related_Object__c = relatedRecordId;
        }

        insert timeLog;
        return timeLog;
    }

    /**
     * @description Gets a list of all users who have time logs
     * @return List of User records with time log summary information
     */
    public static List<Map<String, Object>> getAllTimeLogUsers() {
        List<Map<String, Object>> usersList = new List<Map<String, Object>>();

        try {
            // Check if current user is a System Administrator or Executive Management
            Boolean isAdmin = false;

            // Get current user's profile
            User currentUserWithProfile = [
                SELECT Id, Name, Profile.Name
                FROM User
                WHERE Id = :UserInfo.getUserId()
                LIMIT 1
            ];

            // Check if user has System Administrator or Executive Management profile
            if (currentUserWithProfile.Profile.Name == 'System Administrator' ||
                currentUserWithProfile.Profile.Name.contains('Executive Management')) {
                isAdmin = true;
            }

            // If user is admin, show all active users
            if (isAdmin) {
                // First get all active users
                List<User> allUsers = [
                    SELECT Id, Name, Profile.Name, UserType, IsActive
                    FROM User
                    WHERE IsActive = true
                    ORDER BY Name
                ];

                // Filter out system users
                List<User> filteredUsers = new List<User>();
                for (User u : allUsers) {
                    // Skip system users
                    if (u.Name.startsWith('Automated') ||
                        u.Name.startsWith('Data.com') ||
                        u.Name.startsWith('Chatter') ||
                        u.Name.startsWith('Insights') ||
                        u.Name.startsWith('Integration') ||
                        u.Name.startsWith('Security') ||
                        u.Name.startsWith('Analytics') ||
                        u.Name.startsWith('Platform Integration')) {
                        continue;
                    }
                    filteredUsers.add(u);
                }

                // Use the filtered list
                allUsers = filteredUsers;

                // Create a map to store time log stats for each user
                Map<Id, Map<String, Object>> userStatsMap = new Map<Id, Map<String, Object>>();

                // Initialize map with all users
                for (User u : allUsers) {
                    Map<String, Object> userInfo = new Map<String, Object>{
                        'userId' => u.Id,
                        'userName' => u.Name,
                        'logCount' => 0,
                        'totalHours' => 0
                    };
                    userStatsMap.put(u.Id, userInfo);
                }

                // Query for users with time logs and aggregate their total hours
                AggregateResult[] userResults = [
                    SELECT User__c, User__r.Name, COUNT(Id) logCount,
                           SUM(Duration_Hours__c) totalHours
                    FROM Time_Log__c
                    WHERE End_Time__c != null
                    GROUP BY User__c, User__r.Name
                ];

                // Update the map with time log stats
                for (AggregateResult ar : userResults) {
                    Id userId = (Id)ar.get('User__c');
                    Integer logCount = (Integer)ar.get('logCount');
                    Decimal totalHours = (Decimal)ar.get('totalHours');

                    if (userId != null && userStatsMap.containsKey(userId)) {
                        Map<String, Object> userInfo = userStatsMap.get(userId);
                        userInfo.put('logCount', logCount);
                        userInfo.put('totalHours', totalHours != null ? totalHours.setScale(2) : 0);
                    }
                }

                // Convert map to list
                usersList = userStatsMap.values();

                // If no users found, add current user as placeholder
                if (usersList.isEmpty()) {
                    Map<String, Object> userInfo = new Map<String, Object>{
                        'userId' => currentUserWithProfile.Id,
                        'userName' => currentUserWithProfile.Name,
                        'logCount' => 0,
                        'totalHours' => 0
                    };
                    usersList.add(userInfo);
                }
            } else {
                // Regular user behavior - only show users with time logs
                // Query for users with time logs and aggregate their total hours
                AggregateResult[] userResults = [
                    SELECT User__c, User__r.Name, COUNT(Id) logCount,
                           SUM(Duration_Hours__c) totalHours
                    FROM Time_Log__c
                    WHERE End_Time__c != null
                    GROUP BY User__c, User__r.Name
                    ORDER BY User__r.Name
                ];

                // If no results, return the current user as a placeholder
                if (userResults == null || userResults.isEmpty()) {
                    Map<String, Object> userInfo = new Map<String, Object>{
                        'userId' => currentUserWithProfile.Id,
                        'userName' => currentUserWithProfile.Name,
                        'logCount' => 0,
                        'totalHours' => 0
                    };
                    usersList.add(userInfo);
                    return usersList;
                }

                for (AggregateResult ar : userResults) {
                    Id userId = (Id)ar.get('User__c');
                    String userName = (String)ar.get('User__r.Name');
                    Integer logCount = (Integer)ar.get('logCount');
                    Decimal totalHours = (Decimal)ar.get('totalHours');

                    if (userId != null) {
                        Map<String, Object> userInfo = new Map<String, Object>{
                            'userId' => userId,
                            'userName' => userName,
                            'logCount' => logCount,
                            'totalHours' => totalHours != null ? totalHours.setScale(2) : 0
                        };
                        usersList.add(userInfo);
                    }
                }
            }
        } catch (Exception e) {
            // If any error occurs, return the current user as a placeholder
            User currentUser = [SELECT Id, Name FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];
            Map<String, Object> userInfo = new Map<String, Object>{
                'userId' => currentUser.Id,
                'userName' => currentUser.Name,
                'logCount' => 0,
                'totalHours' => 0
            };
            usersList.add(userInfo);
        }

        return usersList;
    }

    /**
     * @description Gets time log statistics for a specific user
     * @param userId The ID of the user to get statistics for
     * @return Map containing various time log statistics for the specified user
     */
    public static Map<String, Object> getUserTimeLogStats(Id userId) {
        // Call the overloaded method with default parameters
        return getUserTimeLogStats(userId, 'ALL', null, null);
    }

    /**
     * @description Gets time log statistics for a specific user with time filter
     * @param userId The ID of the user to get statistics for
     * @param timeFilter The time filter to apply (e.g., 'THIS_MONTH', 'LAST_MONTH', 'THIS_YEAR', 'CUSTOM')
     * @param startDate The start date for custom time filter
     * @param endDate The end date for custom time filter
     * @return Map containing various time log statistics for the specified user
     */
    public static Map<String, Object> getUserTimeLogStats(
        Id userId,
        String timeFilter,
        Date startDate,
        Date endDate
    ) {
        Map<String, Object> stats = new Map<String, Object>();

        // Apply time filter
        Datetime filterStartDateTime;
        Datetime filterEndDateTime;
        String whereClause = '';

        // Default to ALL if no filter specified
        if (timeFilter == null) {
            timeFilter = 'ALL';
        }

        // Apply the time filter
        if (timeFilter == 'CURRENT_WEEK') {
            // Current week - Monday to Sunday of current week
            Date weekStart = Date.today().toStartOfWeek();
            Date weekEnd = weekStart.addDays(6); // Sunday
            filterStartDateTime = Datetime.newInstance(weekStart, Time.newInstance(0, 0, 0, 0));
            filterEndDateTime = Datetime.newInstance(weekEnd, Time.newInstance(23, 59, 59, 999));
            whereClause = ' AND Start_Time__c >= :filterStartDateTime AND Start_Time__c <= :filterEndDateTime';
        } else if (timeFilter == 'LAST_WEEK') {
            // Last week - Monday to Sunday of previous week
            Date currentWeekStart = Date.today().toStartOfWeek();
            Date lastWeekStart = currentWeekStart.addDays(-7);
            Date lastWeekEnd = lastWeekStart.addDays(6); // Sunday
            filterStartDateTime = Datetime.newInstance(lastWeekStart, Time.newInstance(0, 0, 0, 0));
            filterEndDateTime = Datetime.newInstance(lastWeekEnd, Time.newInstance(23, 59, 59, 999));
            whereClause = ' AND Start_Time__c >= :filterStartDateTime AND Start_Time__c <= :filterEndDateTime';
        } else if (timeFilter == 'THIS_MONTH') {
            Date monthStart = Date.today().toStartOfMonth();
            Date monthEnd = monthStart.addDays(Date.daysInMonth(monthStart.year(), monthStart.month()) - 1);
            filterStartDateTime = Datetime.newInstance(monthStart, Time.newInstance(0, 0, 0, 0));
            filterEndDateTime = Datetime.newInstance(monthEnd, Time.newInstance(23, 59, 59, 999));
            whereClause = ' AND Start_Time__c >= :filterStartDateTime AND Start_Time__c <= :filterEndDateTime';
        } else if (timeFilter == 'LAST_MONTH') {
            Date lastMonthStart = Date.today().toStartOfMonth().addMonths(-1);
            Date lastMonthEnd = lastMonthStart.addDays(Date.daysInMonth(lastMonthStart.year(), lastMonthStart.month()) - 1);
            filterStartDateTime = Datetime.newInstance(lastMonthStart, Time.newInstance(0, 0, 0, 0));
            filterEndDateTime = Datetime.newInstance(lastMonthEnd, Time.newInstance(23, 59, 59, 999));
            whereClause = ' AND Start_Time__c >= :filterStartDateTime AND Start_Time__c <= :filterEndDateTime';
        } else if (timeFilter == 'THIS_YEAR') {
            Date yearStart = Date.newInstance(Date.today().year(), 1, 1);
            Date yearEnd = Date.newInstance(Date.today().year(), 12, 31);
            filterStartDateTime = Datetime.newInstance(yearStart, Time.newInstance(0, 0, 0, 0));
            filterEndDateTime = Datetime.newInstance(yearEnd, Time.newInstance(23, 59, 59, 999));
            whereClause = ' AND Start_Time__c >= :filterStartDateTime AND Start_Time__c <= :filterEndDateTime';
        } else if (timeFilter == 'CUSTOM' && startDate != null && endDate != null) {
            // For custom date range, we need to ensure we're including the entire day
            filterStartDateTime = Datetime.newInstance(startDate.year(), startDate.month(), startDate.day(), 0, 0, 0);
            // Add one day to end date to include the entire end date
            Date adjustedEndDate = endDate.addDays(1);
            filterEndDateTime = Datetime.newInstance(adjustedEndDate.year(), adjustedEndDate.month(), adjustedEndDate.day(), 0, 0, 0);
            whereClause = ' AND Start_Time__c >= :filterStartDateTime AND Start_Time__c < :filterEndDateTime';
        } else if (timeFilter == 'ALL') {
            // No date filter - show all time logs
            whereClause = '';
        }

        // Get total hours and billable hours with time filter
        String totalQuery = 'SELECT SUM(Duration_Hours__c) totalHours FROM Time_Log__c WHERE User__c = :userId AND End_Time__c != null' + whereClause;
        String billableQuery = 'SELECT SUM(Duration_Hours__c) billableHours FROM Time_Log__c WHERE User__c = :userId AND End_Time__c != null AND Billable__c = true' + whereClause;

        AggregateResult[] totalResults = Database.query(totalQuery);
        AggregateResult[] billableResults = Database.query(billableQuery);

        Decimal totalHours = (Decimal)totalResults[0].get('totalHours');
        Decimal billableHours = (Decimal)billableResults[0].get('billableHours');

        stats.put('totalHours', totalHours != null ? totalHours.setScale(2) : 0);
        stats.put('billableHours', billableHours != null ? billableHours.setScale(2) : 0);

        // Get hours by object type with time filter
        String objectTypeQuery = 'SELECT Object_Type__c objectType, SUM(Duration_Hours__c) hours FROM Time_Log__c WHERE User__c = :userId AND End_Time__c != null' +
                               whereClause + ' GROUP BY Object_Type__c ORDER BY SUM(Duration_Hours__c) DESC';

        AggregateResult[] objectTypeResults = Database.query(objectTypeQuery);

        List<Map<String, Object>> byObjectType = new List<Map<String, Object>>();
        for (AggregateResult ar : objectTypeResults) {
            String objectType = (String)ar.get('objectType');
            Decimal hours = (Decimal)ar.get('hours');

            if (objectType != null && hours != null) {
                Map<String, Object> item = new Map<String, Object>{
                    'objectType' => objectType,
                    'hours' => hours.setScale(2)
                };
                byObjectType.add(item);
            }
        }

        stats.put('byObjectType', byObjectType);

        // Get monthly data (last 6 months)
        List<Map<String, Object>> byMonth = new List<Map<String, Object>>();
        Date today = Date.today();

        for (Integer i = 5; i >= 0; i--) {
            Date monthStart = today.toStartOfMonth().addMonths(-i);
            Date monthEnd = monthStart.addDays(Date.daysInMonth(monthStart.year(), monthStart.month()) - 1);

            AggregateResult[] monthlyResults = [
                SELECT SUM(Duration_Hours__c) hours
                FROM Time_Log__c
                WHERE User__c = :userId
                AND End_Time__c != null
                AND Start_Time__c >= :Datetime.newInstance(monthStart, Time.newInstance(0, 0, 0, 0))
                AND Start_Time__c <= :Datetime.newInstance(monthEnd, Time.newInstance(23, 59, 59, 999))
            ];

            Decimal monthHours = (Decimal)monthlyResults[0].get('hours');

            Map<String, Object> monthData = new Map<String, Object>{
                'monthLabel' => monthStart.month() + '/' + monthStart.year(),
                'monthStart' => monthStart,
                'monthEnd' => monthEnd,
                'hours' => monthHours != null ? monthHours.setScale(2) : 0
            };

            byMonth.add(monthData);
        }

        stats.put('byMonth', byMonth);

        return stats;
    }

    /**
     * @description Gets time logs for a specific user with filtering options
     * @param userId The ID of the user to get time logs for
     * @param timeFilter The time filter to apply (e.g., 'THIS_MONTH', 'LAST_MONTH', 'THIS_YEAR', 'CUSTOM')
     * @param startDate The start date for custom time filter
     * @param endDate The end date for custom time filter
     * @param limitCount The maximum number of records to return
     * @return List of Time_Log__c records for the specified user
     */
    public static List<Time_Log__c> getUserTimeLogs(
        Id userId,
        String timeFilter,
        Date startDate,
        Date endDate,
        Integer limitCount
    ) {
        // Validate userId
        if (userId == null) {
            System.debug('ERROR: Null userId passed to getUserTimeLogs in TimeLoggerService');
            return new List<Time_Log__c>();
        }

        // Default to current month if no filter specified
        if (timeFilter == null) {
            timeFilter = 'THIS_MONTH';
        }

        // Default limit if not specified
        if (limitCount == null || limitCount <= 0) {
            limitCount = 100;
        }

        // First, let's check if there are any time logs for this user at all
        List<Time_Log__c> allUserLogs = new List<Time_Log__c>();
        try {
            allUserLogs = [
                SELECT Id, Start_Time__c, End_Time__c, User__c, Object_Type__c, Duration_Hours__c
                FROM Time_Log__c
                WHERE User__c = :userId
                LIMIT 100
            ];
        } catch (Exception e) {
            System.debug('ERROR querying time logs: ' + e.getMessage());
            return new List<Time_Log__c>();
        }

        System.debug('DEBUG - All time logs for user ' + userId + ': ' + allUserLogs.size());
        for (Time_Log__c log : allUserLogs) {
            System.debug('DEBUG - Time log: ' + log.Id + ', User: ' + log.User__c +
                         ', Object Type: ' + log.Object_Type__c +
                         ', Start: ' + log.Start_Time__c +
                         ', End: ' + log.End_Time__c +
                         ', Duration: ' + log.Duration_Hours__c);
        }

        // Build the query
        String query = 'SELECT Id, Object_Type__c, Related_Object__c, Start_Time__c, End_Time__c, ' +
                      'Duration_Hours__c, Description__c, Billable__c, ' +
                      'Account__c, Case__c, Contact__c, Opportunity__c ' +
                      'FROM Time_Log__c ' +
                      'WHERE User__c = :userId ';

        // We want to include all time logs, even those without End_Time__c
        System.debug('DEBUG - Including all time logs, even those without End_Time__c');

        // Add a debug statement to show the current filter settings
        System.debug('DEBUG - Time filter: ' + timeFilter +
                    ', Start date: ' + (startDate != null ? startDate.format() : 'null') +
                    ', End date: ' + (endDate != null ? endDate.format() : 'null'));

        // Apply time filter
        Datetime filterStartDateTime;
        Datetime filterEndDateTime;

        // Use the provided time filter
        String effectiveTimeFilter = timeFilter;
        System.debug('DEBUG - Using time filter: ' + effectiveTimeFilter);

        if (effectiveTimeFilter == 'CURRENT_WEEK') {
            // Current week - Monday to Sunday of current week
            Date weekStart = Date.today().toStartOfWeek();
            Date weekEnd = weekStart.addDays(6); // Sunday
            filterStartDateTime = Datetime.newInstance(weekStart, Time.newInstance(0, 0, 0, 0));
            filterEndDateTime = Datetime.newInstance(weekEnd, Time.newInstance(23, 59, 59, 999));
            query += 'AND Start_Time__c >= :filterStartDateTime AND Start_Time__c <= :filterEndDateTime ';
        } else if (effectiveTimeFilter == 'LAST_WEEK') {
            // Last week - Monday to Sunday of previous week
            Date currentWeekStart = Date.today().toStartOfWeek();
            Date lastWeekStart = currentWeekStart.addDays(-7);
            Date lastWeekEnd = lastWeekStart.addDays(6); // Sunday
            filterStartDateTime = Datetime.newInstance(lastWeekStart, Time.newInstance(0, 0, 0, 0));
            filterEndDateTime = Datetime.newInstance(lastWeekEnd, Time.newInstance(23, 59, 59, 999));
            query += 'AND Start_Time__c >= :filterStartDateTime AND Start_Time__c <= :filterEndDateTime ';
        } else if (effectiveTimeFilter == 'THIS_MONTH') {
            Date monthStart = Date.today().toStartOfMonth();
            Date monthEnd = monthStart.addDays(Date.daysInMonth(monthStart.year(), monthStart.month()) - 1);
            filterStartDateTime = Datetime.newInstance(monthStart, Time.newInstance(0, 0, 0, 0));
            filterEndDateTime = Datetime.newInstance(monthEnd, Time.newInstance(23, 59, 59, 999));
            query += 'AND Start_Time__c >= :filterStartDateTime AND Start_Time__c <= :filterEndDateTime ';
        } else if (effectiveTimeFilter == 'LAST_MONTH') {
            Date lastMonthStart = Date.today().toStartOfMonth().addMonths(-1);
            Date lastMonthEnd = lastMonthStart.addDays(Date.daysInMonth(lastMonthStart.year(), lastMonthStart.month()) - 1);
            filterStartDateTime = Datetime.newInstance(lastMonthStart, Time.newInstance(0, 0, 0, 0));
            filterEndDateTime = Datetime.newInstance(lastMonthEnd, Time.newInstance(23, 59, 59, 999));
            query += 'AND Start_Time__c >= :filterStartDateTime AND Start_Time__c <= :filterEndDateTime ';
        } else if (effectiveTimeFilter == 'THIS_YEAR') {
            Date yearStart = Date.newInstance(Date.today().year(), 1, 1);
            Date yearEnd = Date.newInstance(Date.today().year(), 12, 31);
            filterStartDateTime = Datetime.newInstance(yearStart, Time.newInstance(0, 0, 0, 0));
            filterEndDateTime = Datetime.newInstance(yearEnd, Time.newInstance(23, 59, 59, 999));
            query += 'AND Start_Time__c >= :filterStartDateTime AND Start_Time__c <= :filterEndDateTime ';
        } else if (effectiveTimeFilter == 'CUSTOM' && startDate != null && endDate != null) {
            // For custom date range, we need to ensure we're including the entire day
            filterStartDateTime = Datetime.newInstance(startDate.year(), startDate.month(), startDate.day(), 0, 0, 0);
            // Add one day to end date to include the entire end date
            Date adjustedEndDate = endDate.addDays(1);
            filterEndDateTime = Datetime.newInstance(adjustedEndDate.year(), adjustedEndDate.month(), adjustedEndDate.day(), 0, 0, 0);
            query += 'AND Start_Time__c >= :filterStartDateTime AND Start_Time__c < :filterEndDateTime ';
        } else if (effectiveTimeFilter == 'ALL') {
            // No date filter - show all time logs
            System.debug('DEBUG - Using ALL time filter, no date restrictions');
        }

        query += 'ORDER BY Start_Time__c DESC LIMIT :limitCount';

        // Execute the query
        Map<String, Object> bindVars = new Map<String, Object>{
            'userId' => userId,
            'limitCount' => limitCount
        };

        if (filterStartDateTime != null) {
            bindVars.put('filterStartDateTime', filterStartDateTime);
        }

        if (filterEndDateTime != null) {
            bindVars.put('filterEndDateTime', filterEndDateTime);
        }

        System.debug('DEBUG - Query: ' + query);
        System.debug('DEBUG - Bind variables: ' + bindVars);

        List<Time_Log__c> results = new List<Time_Log__c>();
        try {
            results = Database.query(query);
            System.debug('DEBUG - Query results: ' + results.size());
        } catch (Exception e) {
            System.debug('ERROR executing query: ' + e.getMessage() + ' - ' + e.getStackTraceString());
            // Return empty list instead of throwing exception
            return new List<Time_Log__c>();
        }

        return results;
    }
}
