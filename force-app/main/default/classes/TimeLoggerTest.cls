public class TimeLoggerTest {
    public static void createTimeLog() {
        Time_Log__c timeLog = new Time_Log__c();
        timeLog.Object_Type__c = 'Account';
        timeLog.Start_Time__c = Datetime.now();
        timeLog.End_Time__c = Datetime.now().addHours(1);
        timeLog.Description__c = 'Test time log';
        timeLog.Billable__c = true;
        
        try {
            insert timeLog;
            System.debug('Time Log created successfully: ' + timeLog.Id);
        } catch (Exception e) {
            System.debug('Error creating Time Log: ' + e.getMessage());
        }
    }
}
