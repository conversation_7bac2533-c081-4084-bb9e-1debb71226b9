<?xml version="1.0" encoding="UTF-8"?>
<Dashboard xmlns="http://soap.sforce.com/2006/04/metadata">
    <backgroundEndColor>#FFFFFF</backgroundEndColor>
    <backgroundFadeDirection>Diagonal</backgroundFadeDirection>
    <backgroundStartColor>#FFFFFF</backgroundStartColor>
    <chartTheme>light</chartTheme>
    <colorPalette>unity</colorPalette>
    <dashboardChartTheme>light</dashboardChartTheme>
    <dashboardColorPalette>unity</dashboardColorPalette>
    <dashboardGridLayout>
        <dashboardGridComponents>
            <colSpan>4</colSpan>
            <columnIndex>0</columnIndex>
            <dashboardComponent>
                <autoselectColumnsFromReport>false</autoselectColumnsFromReport>
                <chartAxisRange>Auto</chartAxisRange>
                <chartSummary>
                    <aggregate>Sum</aggregate>
                    <axisBinding>y</axisBinding>
                    <column>Time_Log__c.Duration_Hours__c</column>
                </chartSummary>
                <componentType>Bar</componentType>
                <decimalPrecision>1</decimalPrecision>
                <displayUnits>Auto</displayUnits>
                <drillEnabled>false</drillEnabled>
                <drillToDetailEnabled>false</drillToDetailEnabled>
                <enableHover>false</enableHover>
                <expandOthers>false</expandOthers>
                <groupingColumn>Time_Log__c.User__c</groupingColumn>
                <header>Time by User</header>
                <legendPosition>Bottom</legendPosition>
                <report>Time_Log_Reports/Time_by_User</report>
                <showPercentage>false</showPercentage>
                <showValues>true</showValues>
                <sortBy>RowLabelAscending</sortBy>
                <useReportChart>false</useReportChart>
            </dashboardComponent>
            <rowIndex>0</rowIndex>
            <rowSpan>8</rowSpan>
        </dashboardGridComponents>
        <dashboardGridComponents>
            <colSpan>4</colSpan>
            <columnIndex>4</columnIndex>
            <dashboardComponent>
                <autoselectColumnsFromReport>false</autoselectColumnsFromReport>
                <chartAxisRange>Auto</chartAxisRange>
                <chartSummary>
                    <aggregate>Sum</aggregate>
                    <axisBinding>y</axisBinding>
                    <column>Time_Log__c.Duration_Hours__c</column>
                </chartSummary>
                <componentType>Donut</componentType>
                <decimalPrecision>1</decimalPrecision>
                <displayUnits>Auto</displayUnits>
                <drillEnabled>false</drillEnabled>
                <drillToDetailEnabled>false</drillToDetailEnabled>
                <enableHover>false</enableHover>
                <expandOthers>false</expandOthers>
                <groupingColumn>Time_Log__c.Object_Type__c</groupingColumn>
                <header>Time by Object Type</header>
                <legendPosition>Bottom</legendPosition>
                <report>Time_Log_Reports/Time_by_Object_Type</report>
                <showPercentage>true</showPercentage>
                <showTotal>true</showTotal>
                <showValues>true</showValues>
                <sortBy>RowLabelAscending</sortBy>
                <useReportChart>false</useReportChart>
            </dashboardComponent>
            <rowIndex>0</rowIndex>
            <rowSpan>8</rowSpan>
        </dashboardGridComponents>
        <dashboardGridComponents>
            <colSpan>4</colSpan>
            <columnIndex>8</columnIndex>
            <dashboardComponent>
                <autoselectColumnsFromReport>false</autoselectColumnsFromReport>
                <chartAxisRange>Auto</chartAxisRange>
                <chartSummary>
                    <aggregate>Sum</aggregate>
                    <axisBinding>y</axisBinding>
                    <column>Time_Log__c.Duration_Hours__c</column>
                </chartSummary>
                <componentType>Pie</componentType>
                <decimalPrecision>1</decimalPrecision>
                <displayUnits>Auto</displayUnits>
                <drillEnabled>false</drillEnabled>
                <drillToDetailEnabled>false</drillToDetailEnabled>
                <enableHover>false</enableHover>
                <expandOthers>false</expandOthers>
                <groupingColumn>Time_Log__c.Billable__c</groupingColumn>
                <header>Billable vs. Non-Billable Time</header>
                <legendPosition>Bottom</legendPosition>
                <report>Time_Log_Reports/Billable_vs_NonBillable_Time</report>
                <showPercentage>true</showPercentage>
                <showValues>true</showValues>
                <sortBy>RowLabelAscending</sortBy>
                <useReportChart>false</useReportChart>
            </dashboardComponent>
            <rowIndex>0</rowIndex>
            <rowSpan>8</rowSpan>
        </dashboardGridComponents>
        <numberOfColumns>12</numberOfColumns>
        <rowHeight>36</rowHeight>
    </dashboardGridLayout>
    <dashboardType>SpecifiedUser</dashboardType>
    <isGridLayout>true</isGridLayout>
    <runningUser><EMAIL></runningUser>
    <textColor>#000000</textColor>
    <title>Time Tracking Dashboard</title>
    <titleColor>#000000</titleColor>
    <titleSize>12</titleSize>
</Dashboard>
