<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <description>Flow for creating time log entries</description>
    <interviewLabel>Time Log Entry {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Time Log Entry</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <screens>
        <name>Time_Log_Entry_Screen</name>
        <label>Time Log Entry</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <connector>
            <targetReference>Create_Time_Log</targetReference>
        </connector>
        <fields>
            <name>ObjectTypeField</name>
            <choiceReferences>ObjectTypeChoices</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Object Type</fieldText>
            <fieldType>DropdownBox</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>RelatedRecordField</name>
            <dataType>String</dataType>
            <fieldText>Related Record ID</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>StartTimeField</name>
            <dataType>DateTime</dataType>
            <defaultValue>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </defaultValue>
            <fieldText>Start Time</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>EndTimeField</name>
            <dataType>DateTime</dataType>
            <fieldText>End Time</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>DescriptionField</name>
            <fieldText>Description</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <isRequired>false</isRequired>
        </fields>
        <fields>
            <name>BillableField</name>
            <dataType>Boolean</dataType>
            <defaultValue>
                <booleanValue>false</booleanValue>
            </defaultValue>
            <fieldText>Billable?</fieldText>
            <fieldType>InputField</fieldType>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <recordCreates>
        <name>Create_Time_Log</name>
        <label>Create Time Log</label>
        <locationX>176</locationX>
        <locationY>254</locationY>
        <connector>
            <targetReference>Success_Screen</targetReference>
        </connector>
        <inputAssignments>
            <field>Billable__c</field>
            <value>
                <elementReference>BillableField</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Description__c</field>
            <value>
                <elementReference>DescriptionField</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>End_Time__c</field>
            <value>
                <elementReference>EndTimeField</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Object_Type__c</field>
            <value>
                <elementReference>ObjectTypeField</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Related_Object__c</field>
            <value>
                <elementReference>RelatedRecordField</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Start_Time__c</field>
            <value>
                <elementReference>StartTimeField</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>User__c</field>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Time_Log__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <screens>
        <name>Success_Screen</name>
        <label>Success</label>
        <locationX>176</locationX>
        <locationY>374</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>SuccessMessage</name>
            <fieldText>&lt;p&gt;Time Log created successfully!&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Time_Log_Entry_Screen</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <choices>
        <name>ObjectTypeChoices</name>
        <choiceText>Account</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Account</stringValue>
        </value>
    </choices>
    <choices>
        <name>ObjectTypeChoices</name>
        <choiceText>Opportunity</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Opportunity</stringValue>
        </value>
    </choices>
    <choices>
        <name>ObjectTypeChoices</name>
        <choiceText>Case</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Case</stringValue>
        </value>
    </choices>
    <choices>
        <name>ObjectTypeChoices</name>
        <choiceText>Contact</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Contact</stringValue>
        </value>
    </choices>
    <choices>
        <name>ObjectTypeChoices</name>
        <choiceText>Custom Project</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Custom Project</stringValue>
        </value>
    </choices>
    <choices>
        <name>ObjectTypeChoices</name>
        <choiceText>Other</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Other</stringValue>
        </value>
    </choices>
</Flow>
