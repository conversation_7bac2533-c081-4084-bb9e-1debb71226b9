<template>
    <div class="slds-card">
        <div class="slds-card__header slds-grid">
            <header class="slds-media slds-media_center slds-has-flexi-truncate">
                <div class="slds-media__figure">
                    <lightning-icon icon-name="standard:timesheet_entry" size="medium"></lightning-icon>
                </div>
                <div class="slds-media__body">
                    <h2 class="slds-card__header-title">
                        <span>{title}</span>
                    </h2>
                </div>
                <div class="slds-no-flex">
                    <lightning-button-icon
                        icon-name="utility:refresh"
                        alternative-text="Refresh"
                        title="Refresh"
                        onclick={handleRefresh}>
                    </lightning-button-icon>
                </div>
            </header>
        </div>

        <div class="slds-card__body">
            <template if:true={isLoading}>
                <lightning-spinner alternative-text="Loading" size="medium"></lightning-spinner>
            </template>

            <template if:true={error}>
                <div class="slds-text-color_error slds-var-p-around_medium">
                    Error: {error.message}
                </div>
            </template>

            <template if:false={isLoading}>
                <div class="slds-grid slds-gutters">
                    <!-- Left Panel: Employee List -->
                    <div class="slds-col slds-size_1-of-4 slds-container_fluid slds-is-relative">
                        <div class="slds-box slds-box_x-small slds-var-m-bottom_x-small">
                            <h3 class="slds-text-heading_small slds-var-m-bottom_x-small">Employees</h3>
                            <ul>
                                <template for:each={employees} for:item="employee">
                                    <li key={employee.Id} class="slds-item">
                                        <a href="#" class="custom-is-selected slds-var-p-left_x-small slds-color__background_gray-3" data-id={employee.Id} data-selected={employee.selected} onclick={handleEmployeeSelect}>
                                            {employee.Name}
                                        </a>
                                    </li>
                                </template>
                            </ul>
                        </div>
                    </div>

                    <!-- Right Panel: Employee Details -->
                    <div class="slds-col slds-size_3-of-4">
                        <template if:true={selectedUser}>
                            <!-- Time Log Filter - Moved to top -->
                            <div class="slds-box slds-box_small slds-var-m-bottom_small">
                                <div class="slds-grid slds-gutters slds-grid_vertical-align-end">
                                    <div class="slds-col slds-size_1-of-4">
                                        <lightning-combobox
                                            label="Time Period"
                                            value={timeFilter}
                                            options={timeFilterOptions}
                                            onchange={handleTimeFilterChange}>
                                        </lightning-combobox>
                                    </div>

                                    <template if:true={isCustomDateRange}>
                                        <div class="slds-col slds-size_1-of-4">
                                            <lightning-input
                                                type="date"
                                                label="Start Date"
                                                value={startDate}
                                                onchange={handleStartDateChange}>
                                            </lightning-input>
                                        </div>
                                        <div class="slds-col slds-size_1-of-4">
                                            <lightning-input
                                                type="date"
                                                label="End Date"
                                                value={endDate}
                                                onchange={handleEndDateChange}>
                                            </lightning-input>
                                        </div>
                                    </template>

                                    <div class="slds-col slds-size_1-of-4">
                                        <lightning-button
                                            label="Apply Filter"
                                            variant="brand"
                                            onclick={handleApplyFilter}>
                                        </lightning-button>
                                    </div>
                                </div>
                            </div>

                            <!-- Header with Summary Stats -->
                            <div class="slds-box slds-box_small slds-var-m-bottom_small">
                                <div class="slds-grid slds-gutters">
                                    <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-3">
                                        <div class="slds-text-heading_medium">{selectedUser.userName}</div>
                                    </div>
                                    <div class="slds-col slds-size_1-of-3 slds-text-align_center">
                                        <div class="slds-text-title">Total Hours</div>
                                        <div class="slds-text-heading_medium">{totalHours}</div>
                                    </div>
                                    <div class="slds-col slds-size_1-of-3 slds-text-align_center">
                                        <div class="slds-text-title">Billable Hours</div>
                                        <div class="slds-text-heading_medium">{billableHours} ({billablePercentage}%)</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Charts -->
                            <div class="slds-box slds-box_small slds-var-m-bottom_small">
                                <div class="slds-grid slds-gutters">
                                    <div class="slds-col slds-size_1-of-2">
                                        <div class="chart-container" style="position: relative; height: 250px; width: 100%;">
                                            <canvas class="object-type-chart" style="display: block; width: 100%; height: 100%;"></canvas>
                                        </div>
                                    </div>
                                    <div class="slds-col slds-size_1-of-2">
                                        <div class="chart-container" style="position: relative; height: 250px; width: 100%;">
                                            <canvas class="monthly-chart" style="display: block; width: 100%; height: 100%;"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Time Log List -->
                            <div class="slds-box slds-box_small">
                                <h3 class="slds-text-heading_small slds-var-m-bottom_small">Time Logs</h3>

                                <template if:false={hasTimeLogs}>
                                    <div class="slds-text-align_center slds-var-p-around_medium slds-text-color_weak">
                                        No time logs found for the selected period.
                                    </div>
                                </template>

                                <template if:true={hasTimeLogs}>
                                    <table class="slds-table slds-table_cell-buffer slds-table_bordered">
                                        <thead>
                                            <tr class="slds-line-height_reset">
                                                <th scope="col">
                                                    <div class="slds-truncate" title="Object Type">Object Type</div>
                                                </th>
                                                <th scope="col">
                                                    <div class="slds-truncate" title="Description">Description</div>
                                                </th>
                                                <th scope="col">
                                                    <div class="slds-truncate" title="Start Time">Start Time</div>
                                                </th>
                                                <th scope="col">
                                                    <div class="slds-truncate" title="End Time">End Time</div>
                                                </th>
                                                <th scope="col">
                                                    <div class="slds-truncate" title="Duration (Hours)">Duration (Hours)</div>
                                                </th>
                                                <th scope="col">
                                                    <div class="slds-truncate" title="Billable">Billable</div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <template for:each={timeLogs} for:item="log">
                                                <tr key={log.Id} onclick={navigateToRecord} data-id={log.Id} class="slds-hint-parent">
                                                    <td>
                                                        <div class="slds-grid slds-grid_vertical-align-center">
                                                            <lightning-icon icon-name={log.objectTypeIcon} size="small" class="slds-var-m-right_x-small"></lightning-icon>
                                                            <span>{log.Object_Type__c}</span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="slds-truncate" title={log.Description__c}>{log.Description__c}</div>
                                                    </td>
                                                    <td>
                                                        <div class="slds-truncate" title={log.formattedStartTime}>{log.formattedStartTime}</div>
                                                    </td>
                                                    <td>
                                                        <div class="slds-truncate" title={log.formattedEndTime}>{log.formattedEndTime}</div>
                                                    </td>
                                                    <td>
                                                        <div class="slds-truncate" title={log.Duration_Hours__c}>{log.Duration_Hours__c}</div>
                                                    </td>
                                                    <td>
                                                        <template if:true={log.Billable__c}>
                                                            <lightning-icon
                                                                icon-name="utility:check"
                                                                size="small"
                                                                variant="success">
                                                            </lightning-icon>
                                                        </template>
                                                        <template if:false={log.Billable__c}>
                                                            <lightning-icon
                                                                icon-name="utility:close"
                                                                size="small"
                                                                variant="error">
                                                            </lightning-icon>
                                                        </template>
                                                    </td>
                                                </tr>
                                            </template>
                                        </tbody>
                                    </table>
                                </template>
                            </div>
                        </template>

                        <template if:false={selectedUser}>
                            <div class="slds-text-align_center slds-var-p-around_medium slds-text-color_weak">
                                Select an employee from the list to view details.
                            </div>
                        </template>
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>
