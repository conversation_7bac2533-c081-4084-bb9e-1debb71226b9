import { LightningElement, api, wire, track } from 'lwc';
import { refreshApex } from '@salesforce/apex';
import { loadScript } from 'lightning/platformResourceLoader';
import chartjs from '@salesforce/resourceUrl/chartjs';
import getAllTimeLogUsers from '@salesforce/apex/TimeLoggerController.getAllTimeLogUsers';
import getUserTimeLogStats from '@salesforce/apex/TimeLoggerController.getUserTimeLogStats';
import getUserTimeLogs from '@salesforce/apex/TimeLoggerController.getUserTimeLogs';
import { NavigationMixin } from 'lightning/navigation';

export default class EmployeeTimeLogDashboard extends NavigationMixin(LightningElement) {
    // Public properties
    @api title = 'Employee Time Log Dashboard';
    @api defaultTimeFilter = 'THIS_MONTH';
    @api recordsPerPage = 20;

    // Private properties
    @track users = [];
    @track selectedUser;
    @track selectedUserId;
    @track userStats;
    @track timeLogs = [];
    @track timeFilter = 'THIS_MONTH';
    @track startDate;
    @track endDate;
    @track isCustomDateRange = false;
    @track isLoading = true;
    @track error;

    // Chart properties
    chartjsInitialized = false;
    objectTypeChart;
    monthlyChart;

    // Wired properties
    wiredUsersResult;
    wiredUserStatsResult;
    wiredTimeLogsResult;

    // Lifecycle hooks
    connectedCallback() {
        this.timeFilter = this.defaultTimeFilter;
        this.loadChartJS();

        // Add window resize listener to redraw charts when window size changes
        window.addEventListener('resize', this.handleResize.bind(this));
    }

    disconnectedCallback() {
        // Remove window resize listener
        window.removeEventListener('resize', this.handleResize.bind(this));
    }

    handleResize() {
        // Debounce resize event
        if (this.resizeTimeout) {
            clearTimeout(this.resizeTimeout);
        }

        this.resizeTimeout = setTimeout(() => {
            console.log('Window resized, reinitializing charts');
            if (this.chartjsInitialized && this.userStats) {
                this.initializeCharts();
            }
        }, 250);
    }

    // Computed properties
    get hasTimeLogs() {
        console.log('Checking hasTimeLogs: ', this.timeLogs ? this.timeLogs.length : 0);
        return this.timeLogs && this.timeLogs.length > 0;
    }

    get employees() {
        return this.users.map(user => ({
            ...user,
            selected: user.Id === this.selectedUserId
        }));
    }

    // Wire methods
    @wire(getAllTimeLogUsers)
    wiredUsers(result) {
        this.wiredUsersResult = result;
        this.isLoading = true;

        if (result.data) {
            // Process user data
            this.users = result.data;
            this.error = undefined;

            // Select the first user by default if available
            if (this.users.length > 0 && !this.selectedUserId) {
                this.selectedUserId = this.users[0].userId;
                this.selectedUser = this.users[0];
            }

            this.isLoading = false;
        } else if (result.error) {
            this.error = result.error;
            this.users = [];
            this.isLoading = false;
        }
    }

    @wire(getUserTimeLogStats, {
        userId: '$selectedUserId',
        timeFilter: '$timeFilter',
        startDate: '$startDate',
        endDate: '$endDate'
    })
    wiredUserStats(result) {
        this.wiredUserStatsResult = result;
        console.log('wiredUserStats called with result:', result.data ? 'data received' : 'no data');
        console.log('wiredUserStats params - userId:', this.selectedUserId, 'timeFilter:', this.timeFilter);

        if (result.data) {
            this.userStats = result.data;
            this.error = undefined;
            console.log('User stats received:', JSON.stringify(result.data));

            // Initialize charts if ChartJS is loaded
            if (this.chartjsInitialized) {
                console.log('Chart.js is initialized, calling initializeCharts');
                // Add a small delay to ensure DOM is ready
                setTimeout(() => {
                    this.initializeCharts();
                }, 250);
            } else {
                console.log('Chart.js not yet initialized, charts will be initialized later');
            }
        } else if (result.error) {
            console.error('Error in wiredUserStats:', result.error);
            this.error = result.error;
            this.userStats = undefined;
        }
    }

    @wire(getUserTimeLogs, {
        userId: '$selectedUserId',
        timeFilter: '$timeFilter',
        startDate: '$startDate',
        endDate: '$endDate',
        limitCount: '$recordsPerPage'
    })
    wiredTimeLogs(result) {
        this.wiredTimeLogsResult = result;
        console.log('Wire getUserTimeLogs called with userId:', this.selectedUserId);
        console.log('Wire getUserTimeLogs result:', JSON.stringify(result));

        if (result.data) {
            console.log('Wire getUserTimeLogs data received, count:', result.data.length);
            this.timeLogs = result.data.map(log => {
                return {
                    ...log,
                    formattedStartTime: this.formatDateTime(log.Start_Time__c),
                    formattedEndTime: this.formatDateTime(log.End_Time__c),
                    objectTypeIcon: this.getObjectIcon(log.Object_Type__c)
                };
            });
            console.log('Processed time logs:', this.timeLogs.length);
            this.error = undefined;
        } else if (result.error) {
            console.error('Wire getUserTimeLogs error:', result.error);
            this.error = result.error;
            this.timeLogs = [];
        }
    }

    // Event handlers
    handleUserSelect(event) {
        const userId = event.currentTarget.dataset.id;
        console.log('Selected user ID:', userId);

        // Validate user ID
        if (!userId) {
            console.error('No user ID in dataset');
            return;
        }

        this.selectedUserId = userId;
        this.selectedUser = this.users.find(user => user.userId === userId);
        console.log('Selected user object:', JSON.stringify(this.selectedUser));

        // Load time logs for the selected user
        this.isLoading = true;
        console.log('Loading time logs for user:', userId);
        console.log('With filter:', this.timeFilter);

        // Call the Apex method directly to get time logs
        getUserTimeLogs({
            userId: this.selectedUserId,
            timeFilter: this.timeFilter,
            startDate: this.startDate,
            endDate: this.endDate,
            limitCount: this.recordsPerPage
        })
        .then(result => {
            console.log('Time logs fetched on user select:', result ? result.length : 0);
            console.log('Time logs data:', JSON.stringify(result));
            if (result) {
                this.timeLogs = result.map(log => {
                    return {
                        ...log,
                        formattedStartTime: this.formatDateTime(log.Start_Time__c),
                        formattedEndTime: this.formatDateTime(log.End_Time__c),
                        objectTypeIcon: this.getObjectIcon(log.Object_Type__c)
                    };
                });
                console.log('Processed time logs:', this.timeLogs.length);
            }
            this.error = undefined;
        })
        .catch(error => {
            this.error = error;
            console.error('Error fetching time logs on user select:', error);
            console.error('Error details:', JSON.stringify(error));
            this.timeLogs = [];
        })
        .finally(() => {
            this.isLoading = false;
        });
    }

    handleEmployeeSelect(event) {
        const selectedId = event.currentTarget.dataset.id;
        this.selectedUserId = selectedId;
        // Additional logic for selection
    }

    isSelectedUser(userId) {
        return userId === this.selectedUserId;
    }

    handleTimeFilterChange(event) {
        this.timeFilter = event.detail.value;
        this.isCustomDateRange = this.timeFilter === 'CUSTOM';

        // If not custom date range, automatically apply the filter
        if (!this.isCustomDateRange && this.selectedUserId) {
            console.log('Time filter changed to non-custom value, automatically applying filter');
            // Use setTimeout to allow the UI to update first
            setTimeout(() => {
                this.handleApplyFilter();
            }, 0);
        }
    }

    handleStartDateChange(event) {
        this.startDate = event.target.value;
    }

    handleEndDateChange(event) {
        this.endDate = event.target.value;
    }

    handleApplyFilter() {
        // Refresh the time logs data
        this.isLoading = true;
        console.log('Apply filter clicked. Filter:', this.timeFilter);
        console.log('Start date:', this.startDate);
        console.log('End date:', this.endDate);
        console.log('Selected user ID:', this.selectedUserId);

        // Force a re-fetch of time logs by calling the Apex method directly
        if (this.selectedUserId) {
            console.log('Calling getUserTimeLogs with params:', {
                userId: this.selectedUserId,
                timeFilter: this.timeFilter,
                startDate: this.startDate,
                endDate: this.endDate,
                limitCount: this.recordsPerPage
            });

            // First, refresh the user stats to update the chart data
            getUserTimeLogStats({
                userId: this.selectedUserId,
                timeFilter: this.timeFilter,
                startDate: this.startDate,
                endDate: this.endDate
            })
            .then(statsResult => {
                console.log('User stats refreshed after filter change');
                this.userStats = statsResult;

                // Then get the time logs
                return getUserTimeLogs({
                    userId: this.selectedUserId,
                    timeFilter: this.timeFilter,
                    startDate: this.startDate,
                    endDate: this.endDate,
                    limitCount: this.recordsPerPage
                });
            })
            .then(result => {
                console.log('Time logs fetched:', result ? result.length : 0);
                console.log('Time logs data:', JSON.stringify(result));

                if (result) {
                    this.timeLogs = result.map(log => {
                        return {
                            ...log,
                            formattedStartTime: this.formatDateTime(log.Start_Time__c),
                            formattedEndTime: this.formatDateTime(log.End_Time__c),
                            objectTypeIcon: this.getObjectIcon(log.Object_Type__c)
                        };
                    });
                    console.log('Processed time logs:', this.timeLogs.length);
                }
                this.error = undefined;

                // Reinitialize charts with the new data
                if (this.chartjsInitialized) {
                    console.log('Reinitializing charts after filter change');
                    // Add a small delay to ensure DOM is ready
                    setTimeout(() => {
                        this.initializeCharts();
                    }, 250);
                }
            })
            .catch(error => {
                this.error = error;
                console.error('Error fetching data after filter change:', error);
                console.error('Error details:', JSON.stringify(error));
                this.timeLogs = [];
            })
            .finally(() => {
                this.isLoading = false;
            });
        } else {
            console.log('No user selected, skipping time log fetch');
            this.isLoading = false;
        }
    }

    handleRefresh() {
        // Refresh all data
        this.isLoading = true;
        console.log('Refresh button clicked');

        // Use Promise.all to refresh all data sources
        Promise.all([
            refreshApex(this.wiredUsersResult),
            refreshApex(this.wiredUserStatsResult),
            refreshApex(this.wiredTimeLogsResult)
        ])
        .then(() => {
            console.log('Data refreshed successfully');
            // Force a re-fetch of time logs by calling the Apex method directly
            if (this.selectedUserId) {
                return getUserTimeLogs({
                    userId: this.selectedUserId,
                    timeFilter: this.timeFilter,
                    startDate: this.startDate,
                    endDate: this.endDate,
                    limitCount: this.recordsPerPage
                });
            }
            return Promise.resolve([]);
        })
        .then(result => {
            if (result && result.length) {
                this.timeLogs = result.map(log => {
                    return {
                        ...log,
                        formattedStartTime: this.formatDateTime(log.Start_Time__c),
                        formattedEndTime: this.formatDateTime(log.End_Time__c),
                        objectTypeIcon: this.getObjectIcon(log.Object_Type__c)
                    };
                });
            }
        })
        .catch(error => {
            this.error = error;
            console.error('Error refreshing data:', error);
        })
        .finally(() => {
            this.isLoading = false;
        });
    }

    navigateToRecord(event) {
        const recordId = event.currentTarget.dataset.id;
        this[NavigationMixin.Navigate]({
            type: 'standard__recordPage',
            attributes: {
                recordId: recordId,
                objectApiName: 'Time_Log__c',
                actionName: 'view'
            }
        });
    }

    // Helper methods
    formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '';

        const dateTime = new Date(dateTimeStr);
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        }).format(dateTime);
    }

    getObjectIcon(objectType) {
        const iconMap = {
            'Account': 'standard:account',
            'Assessment__c': 'standard:calibration',
            'Campaign': 'standard:campaign',
            'Case': 'standard:case',
            'Case_Activity__c': 'standard:task',
            'Contact': 'standard:contact',
            'Custom Project': 'standard:custom',
            'Opportunity': 'standard:opportunity',
            'Potential__c': 'standard:lead',
            'Recognition__c': 'standard:reward',
            'Other': 'standard:default'
        };

        return iconMap[objectType] || 'standard:default';
    }

    // Chart methods
    loadChartJS() {
        loadScript(this, chartjs)
            .then(() => {
                console.log('Chart.js loaded successfully');
                this.chartjsInitialized = true;

                // Initialize charts if user stats are already loaded
                if (this.userStats) {
                    // Add a small delay to ensure DOM is ready
                    setTimeout(() => {
                        this.initializeCharts();
                    }, 500);
                }
            })
            .catch(error => {
                console.error('Error loading Chart.js:', error);
                this.error = error;
            });
    }

    renderedCallback() {
        // If charts are initialized but not rendered, try to render them
        if (this.chartjsInitialized && this.userStats &&
            (!this.objectTypeChart || !this.monthlyChart)) {
            console.log('Attempting to initialize charts in renderedCallback');
            // Add a small delay to ensure DOM is fully rendered
            setTimeout(() => {
                this.initializeCharts();
            }, 500);
        }
    }

    initializeCharts() {
        console.log('initializeCharts called');
        if (!this.userStats) {
            console.log('No user stats available, skipping chart initialization');
            return;
        }

        if (!this.chartjsInitialized) {
            console.log('Chart.js not initialized yet, skipping chart initialization');
            return;
        }

        // Check if the DOM elements are ready
        const objectTypeCanvas = this.template.querySelector('.object-type-chart');
        const monthlyCanvas = this.template.querySelector('.monthly-chart');

        if (!objectTypeCanvas || !monthlyCanvas) {
            console.log('Chart canvas elements not found, will retry later');
            // Schedule another attempt
            setTimeout(() => {
                this.initializeCharts();
            }, 500);
            return;
        }

        console.log('Initializing charts with canvas elements found');
        this.initializeObjectTypeChart();
        this.initializeMonthlyChart();
    }

    initializeObjectTypeChart() {
        const objectTypeData = this.userStats.byObjectType || [];
        console.log('Object Type Data:', JSON.stringify(objectTypeData));

        if (objectTypeData.length === 0) {
            console.log('No object type data available for chart');
            return;
        }

        const canvas = this.template.querySelector('.object-type-chart');
        if (!canvas) {
            console.error('Object type chart canvas element not found');
            return;
        }

        // Destroy existing chart if it exists
        if (this.objectTypeChart) {
            console.log('Destroying existing object type chart');
            this.objectTypeChart.destroy();
        }

        // Prepare data for chart
        const labels = objectTypeData.map(item => item.objectType);
        const data = objectTypeData.map(item => item.hours);
        console.log('Chart labels:', labels);
        console.log('Chart data:', data);

        const backgroundColors = [
            '#4bc0c0', '#36a2eb', '#ffcd56', '#ff6384', '#9966ff', '#c9cbcf'
        ];

        // Create chart
        try {
            console.log('Creating object type chart');
            this.objectTypeChart = new window.Chart(canvas, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Hours by Object Type',
                        data: data,
                        backgroundColor: backgroundColors.slice(0, labels.length)
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    legend: {
                        position: 'bottom'
                    },
                    title: {
                        display: true,
                        text: 'Hours by Object Type'
                    }
                }
            });
            console.log('Object type chart created successfully');
        } catch (error) {
            console.error('Error creating object type chart:', error);
        }
    }

    initializeMonthlyChart() {
        const monthlyData = this.userStats.byMonth || [];
        console.log('Monthly Data:', JSON.stringify(monthlyData));

        if (monthlyData.length === 0) {
            console.log('No monthly data available for chart');
            return;
        }

        const canvas = this.template.querySelector('.monthly-chart');
        if (!canvas) {
            console.error('Monthly chart canvas element not found');
            return;
        }

        // Destroy existing chart if it exists
        if (this.monthlyChart) {
            console.log('Destroying existing monthly chart');
            this.monthlyChart.destroy();
        }

        // Prepare data for chart
        const labels = monthlyData.map(item => item.monthLabel);
        const data = monthlyData.map(item => item.hours);
        console.log('Monthly chart labels:', labels);
        console.log('Monthly chart data:', data);

        // Create chart
        try {
            console.log('Creating monthly chart');
            this.monthlyChart = new window.Chart(canvas, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Hours by Month',
                        data: data,
                        backgroundColor: '#36a2eb'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Hours by Month'
                    },
                    scales: {
                        yAxes: [{
                            ticks: {
                                beginAtZero: true
                            }
                        }]
                    }
                }
            });
            console.log('Monthly chart created successfully');
        } catch (error) {
            console.error('Error creating monthly chart:', error);
        }
    }

    // Getters
    get timeFilterOptions() {
        return [
            { label: 'Current Week', value: 'CURRENT_WEEK' },
            { label: 'Last Week', value: 'LAST_WEEK' },
            { label: 'This Month', value: 'THIS_MONTH' },
            { label: 'Last Month', value: 'LAST_MONTH' },
            { label: 'This Year', value: 'THIS_YEAR' },
            { label: 'Custom Date Range', value: 'CUSTOM' },
            { label: 'All Time', value: 'ALL' }
        ];
    }

    get hasUsers() {
        return this.users && this.users.length > 0;
    }

    get totalHours() {
        return this.userStats ? this.userStats.totalHours : 0;
    }

    get billableHours() {
        return this.userStats ? this.userStats.billableHours : 0;
    }

    get billablePercentage() {
        if (!this.userStats || !this.userStats.totalHours || this.userStats.totalHours === 0) {
            return 0;
        }
        return Math.round((this.userStats.billableHours / this.userStats.totalHours) * 100);
    }


}
