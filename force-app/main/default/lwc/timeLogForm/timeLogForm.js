import { LightningElement, track, api, wire } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { createRecord } from 'lightning/uiRecordApi';
import { getObjectInfo } from 'lightning/uiObjectInfoApi';
import TIME_LOG_OBJECT from '@salesforce/schema/Time_Log__c';
import OBJECT_TYPE_FIELD from '@salesforce/schema/Time_Log__c.Object_Type__c';
import START_TIME_FIELD from '@salesforce/schema/Time_Log__c.Start_Time__c';
import END_TIME_FIELD from '@salesforce/schema/Time_Log__c.End_Time__c';
import DESCRIPTION_FIELD from '@salesforce/schema/Time_Log__c.Description__c';
import BILLABLE_FIELD from '@salesforce/schema/Time_Log__c.Billable__c';
import RELATED_OBJECT_FIELD from '@salesforce/schema/Time_Log__c.Related_Object__c';
import CASE_FIELD from '@salesforce/schema/Time_Log__c.Case__c';
import ACCOUNT_FIELD from '@salesforce/schema/Time_Log__c.Account__c';
import CONTACT_FIELD from '@salesforce/schema/Time_Log__c.Contact__c';
import OPPORTUNITY_FIELD from '@salesforce/schema/Time_Log__c.Opportunity__c';
import USER_FIELD from '@salesforce/schema/Time_Log__c.User__c';
import USER_ID from '@salesforce/user/Id';

export default class TimeLogForm extends LightningElement {
    // Properties to capture record context
    @api recordId;
    @api objectApiName;

    @track objectType = '';
    @track startTime = null;
    @track endTime = null;
    @track description = '';
    @track billable = false;
    @track relatedObject = '';
    @track fieldAccessError = false;
    @track isLoading = true;
    @track fieldAccessibility = {};

    // Get Time_Log__c object info to check field accessibility
    @wire(getObjectInfo, { objectApiName: TIME_LOG_OBJECT })
    timeLogObjectInfo({ error, data }) {
        if (data) {
            this.isLoading = false;

            // Check object accessibility
            const isAccessible = data.queryable;
            const isCreateable = data.createable;

            if (!isAccessible || !isCreateable) {
                this.fieldAccessError = true;
                console.error('Time_Log__c object is not accessible or createable by the current user');
                return;
            }

            // Check field accessibility
            const fields = data.fields;
            this.fieldAccessibility = {
                objectType: fields[OBJECT_TYPE_FIELD.fieldApiName] ? fields[OBJECT_TYPE_FIELD.fieldApiName].createable : false,
                startTime: fields[START_TIME_FIELD.fieldApiName] ? fields[START_TIME_FIELD.fieldApiName].createable : false,
                endTime: fields[END_TIME_FIELD.fieldApiName] ? fields[END_TIME_FIELD.fieldApiName].createable : false,
                description: fields[DESCRIPTION_FIELD.fieldApiName] ? fields[DESCRIPTION_FIELD.fieldApiName].createable : false,
                billable: fields[BILLABLE_FIELD.fieldApiName] ? fields[BILLABLE_FIELD.fieldApiName].createable : false,
                relatedObject: fields[RELATED_OBJECT_FIELD.fieldApiName] ? fields[RELATED_OBJECT_FIELD.fieldApiName].createable : false
            };

            console.log('Field Accessibility:', this.fieldAccessibility);

            // Check if Object_Type__c field is accessible and createable
            if (!this.fieldAccessibility.objectType) {
                this.fieldAccessError = true;
                console.error('Object_Type__c field is not accessible to the current user');
            }
        } else if (error) {
            this.isLoading = false;
            console.error('Error loading Time_Log__c object info:', error);

            // Show error toast
            this.dispatchEvent(
                new ShowToastEvent({
                    title: 'Error Checking Field Access',
                    message: error.body ? error.body.message : 'Unknown error checking field accessibility',
                    variant: 'error'
                })
            );

            // Set field access error to true as a precaution
            this.fieldAccessError = true;
        }
    }

    // Initialize component with record context if available
    connectedCallback() {
        // Set default values for date/time fields
        const now = new Date();
        this.startTime = now.toISOString();

        // If we're on a record page, set the object type and related record ID
        if (this.recordId && this.objectApiName) {
            // Map the object API name to the picklist value
            const objectTypeMapping = {
                'Case': 'Case',
                'Account': 'Account',
                'Contact': 'Contact',
                'Opportunity': 'Opportunity',
                'Potential__c': 'Potential',
                'Assessment__c': 'Assessment',
                'Campaign': 'Campaign',
                'Case_Activity__c': 'Case Activity',
                'Recognition__c': 'Recognition',
                'Task': 'Case',  // For Case Activity
                'Event': 'Case'  // For Case Activity
            };

            // Get the mapped object type or use 'Other' as fallback
            this.objectType = objectTypeMapping[this.objectApiName] || 'Other';
            this.relatedObject = this.recordId;

            // For debugging
            console.log('Record Context:', {
                recordId: this.recordId,
                objectApiName: this.objectApiName,
                mappedObjectType: this.objectType
            });
        }
    }

    // Called after the component renders
    renderedCallback() {
        // Double-check that our values are set if we're on a record page
        if (this.recordId && this.objectApiName && !this.objectType) {
            this.connectedCallback();
        }
    }

    get objectTypeOptions() {
        return [
            { label: 'Account', value: 'Account' },
            { label: 'Opportunity', value: 'Opportunity' },
            { label: 'Case', value: 'Case' },
            { label: 'Contact', value: 'Contact' },
            { label: 'Potential', value: 'Potential' },
            { label: 'Assessment', value: 'Assessment' },
            { label: 'Campaign', value: 'Campaign' },
            { label: 'Case Activity', value: 'Case Activity' },
            { label: 'Custom Project', value: 'Custom Project' },
            { label: 'Recognition', value: 'Recognition' },
            { label: 'Other', value: 'Other' }
        ];
    }

    get isButtonDisabled() {
        return this.isLoading || this.fieldAccessError;
    }

    handleObjectTypeChange(event) {
        this.objectType = event.detail.value;
    }

    handleStartTimeChange(event) {
        this.startTime = event.target.value;
    }

    handleEndTimeChange(event) {
        this.endTime = event.target.value;
    }

    handleDescriptionChange(event) {
        this.description = event.target.value;
    }

    handleBillableChange(event) {
        this.billable = event.target.checked;
    }

    handleRelatedObjectChange(event) {
        this.relatedObject = event.target.value;
    }

    handleSubmit() {
        // Check if we're still loading field accessibility info
        if (this.isLoading) {
            this.dispatchEvent(
                new ShowToastEvent({
                    title: 'Please wait',
                    message: 'Still loading field information. Please try again in a moment.',
                    variant: 'info'
                })
            );
            return;
        }

        // Check if we already know there's a field access error
        if (this.fieldAccessError) {
            this.dispatchEvent(
                new ShowToastEvent({
                    title: 'Field Access Error',
                    message: 'The Object_Type__c field is not accessible to your user profile. Please contact your administrator to update field-level security settings.',
                    variant: 'error'
                })
            );
            return;
        }

        try {
            const fields = {};

            // Try to add all fields, but catch any errors
            try {
                fields[OBJECT_TYPE_FIELD.fieldApiName] = this.objectType;
                fields[START_TIME_FIELD.fieldApiName] = this.startTime;
                fields[END_TIME_FIELD.fieldApiName] = this.endTime;
                fields[DESCRIPTION_FIELD.fieldApiName] = this.description;
                fields[BILLABLE_FIELD.fieldApiName] = this.billable;
                fields[RELATED_OBJECT_FIELD.fieldApiName] = this.relatedObject;
                fields[USER_FIELD.fieldApiName] = USER_ID; // Always set the current user ID
            } catch (fieldError) {
                console.error('Error setting fields:', fieldError);
                // Continue with the fields we were able to set
            }

            // Set the specific lookup field based on object type
            if (this.relatedObject) {
                try {
                    // Map object types to their corresponding lookup fields
                    const lookupFieldMapping = {
                        'Case': CASE_FIELD.fieldApiName,
                        'Account': ACCOUNT_FIELD.fieldApiName,
                        'Contact': CONTACT_FIELD.fieldApiName,
                        'Opportunity': OPPORTUNITY_FIELD.fieldApiName,
                        'Potential': 'Potential__c'
                    };

                    // If we have a mapping for this object type, set the lookup field
                    const lookupField = lookupFieldMapping[this.objectType];
                    if (lookupField) {
                        fields[lookupField] = this.relatedObject;
                    }

                    // For Task and Event (Case Activity), we need to handle them specially
                    if (this.objectApiName === 'Task' || this.objectApiName === 'Event') {
                        fields[CASE_FIELD.fieldApiName] = this.relatedObject;
                    }
                } catch (lookupError) {
                    console.error('Error setting lookup field:', lookupError);
                    // Continue without the lookup field
                }
            }

            // Log the fields we're sending for debugging
            console.log('Creating Time Log with fields:', fields);

            const recordInput = { apiName: TIME_LOG_OBJECT.objectApiName, fields };

            createRecord(recordInput)
                .then(timeLog => {
                    this.dispatchEvent(
                        new ShowToastEvent({
                            title: 'Success',
                            message: 'Time Log created',
                            variant: 'success'
                        })
                    );
                    // Reset form
                    this.objectType = '';
                    this.startTime = null;
                    this.endTime = null;
                    this.description = '';
                    this.billable = false;
                    this.relatedObject = '';
                })
                .catch(error => {
                    console.error('Error creating record:', error);

                    // Enhanced error handling with more specific messages
                    if (error.body) {
                        if (error.body.message && error.body.message.includes('does not exist')) {
                            this.dispatchEvent(
                                new ShowToastEvent({
                                    title: 'Field Missing',
                                    message: 'One or more fields are not available in your org. This may be because the Object_Type__c field has not been deployed yet. Please contact your administrator.',
                                    variant: 'error'
                                })
                            );
                        } else if (error.body.message && error.body.message.includes('insufficient access rights')) {
                            this.dispatchEvent(
                                new ShowToastEvent({
                                    title: 'Access Error',
                                    message: 'You do not have permission to create Time Log records or access required fields. Please contact your administrator to update your permissions.',
                                    variant: 'error'
                                })
                            );
                        } else if (error.body.message && error.body.message.includes('FIELD_CUSTOM_VALIDATION_EXCEPTION')) {
                            this.dispatchEvent(
                                new ShowToastEvent({
                                    title: 'Validation Error',
                                    message: error.body.message.replace('FIELD_CUSTOM_VALIDATION_EXCEPTION, ', ''),
                                    variant: 'error'
                                })
                            );
                        } else {
                            this.dispatchEvent(
                                new ShowToastEvent({
                                    title: 'Error creating record',
                                    message: error.body.message || 'Unknown error',
                                    variant: 'error'
                                })
                            );
                        }
                    } else {
                        this.dispatchEvent(
                            new ShowToastEvent({
                                title: 'Error creating record',
                                message: 'An unknown error occurred. Please try again or contact your administrator.',
                                variant: 'error'
                            })
                        );
                    }
                });
        } catch (error) {
            console.error('Exception in handleSubmit:', error);
            this.dispatchEvent(
                new ShowToastEvent({
                    title: 'Error preparing record',
                    message: error.message || 'Unknown error',
                    variant: 'error'
                })
            );
        }
    }
}
