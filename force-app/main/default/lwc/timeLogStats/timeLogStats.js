import { LightningElement, wire } from 'lwc';
import { loadScript } from 'lightning/platformResourceLoader';
import chartjs from '@salesforce/resourceUrl/chartjs';
import getTimeLogStats from '@salesforce/apex/TimeLoggerController.getTimeLogStats';

export default class TimeLogStats extends LightningElement {
    chartjsInitialized = false;
    isLoading = true;
    error;
    
    // Stats data
    totalHours = 0;
    billableHours = 0;
    billablePercentage = 0;
    objectTypeData = [];
    weeklyData = [];
    
    // Chart configurations
    objectTypeChartConfig;
    weeklyChartConfig;
    
    // References to chart canvases
    objectTypeChart;
    weeklyChart;
    
    // Wire the getTimeLogStats method from the Apex controller
    @wire(getTimeLogStats)
    wiredTimeLogStats({ error, data }) {
        if (data) {
            this.processStats(data);
            this.error = undefined;
        } else if (error) {
            this.error = error;
            this.isLoading = false;
        }
    }
    
    // Process the stats data
    processStats(data) {
        this.totalHours = data.totalHours || 0;
        this.billableHours = data.billableHours || 0;
        this.billablePercentage = this.totalHours > 0 ? Math.round((this.billableHours / this.totalHours) * 100) : 0;
        
        // Process object type data for chart
        this.objectTypeData = data.byObjectType || [];
        
        // Process weekly data for chart
        this.weeklyData = data.byWeek || [];
        
        // Initialize charts if ChartJS is loaded
        if (this.chartjsInitialized) {
            this.initializeCharts();
        }
        
        this.isLoading = false;
    }
    
    // Lifecycle hook when component is connected
    connectedCallback() {
        this.loadChartJS();
    }
    
    // Load ChartJS library
    loadChartJS() {
        if (this.chartjsInitialized) {
            return;
        }
        
        this.isLoading = true;
        
        loadScript(this, chartjs)
            .then(() => {
                this.chartjsInitialized = true;
                this.initializeCharts();
            })
            .catch(error => {
                this.error = error;
            })
            .finally(() => {
                this.isLoading = false;
            });
    }
    
    // Initialize charts
    initializeCharts() {
        if (!this.chartjsInitialized || this.isLoading || !this.objectTypeData.length) {
            return;
        }
        
        // Initialize object type chart
        this.initializeObjectTypeChart();
        
        // Initialize weekly chart
        this.initializeWeeklyChart();
    }
    
    // Initialize object type chart
    initializeObjectTypeChart() {
        const canvas = this.template.querySelector('canvas.object-type-chart');
        if (!canvas) return;
        
        // Prepare data for chart
        const labels = this.objectTypeData.map(item => item.objectType);
        const data = this.objectTypeData.map(item => item.hours);
        
        // Define colors
        const backgroundColors = [
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 99, 132, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 159, 64, 0.6)'
        ];
        
        // Create chart configuration
        this.objectTypeChartConfig = {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: backgroundColors.slice(0, labels.length),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    position: 'right',
                    labels: {
                        boxWidth: 12
                    }
                },
                title: {
                    display: true,
                    text: 'Hours by Object Type'
                }
            }
        };
        
        // Create chart
        if (this.objectTypeChart) {
            this.objectTypeChart.destroy();
        }
        this.objectTypeChart = new Chart(canvas, this.objectTypeChartConfig);
    }
    
    // Initialize weekly chart
    initializeWeeklyChart() {
        const canvas = this.template.querySelector('canvas.weekly-chart');
        if (!canvas) return;
        
        // Prepare data for chart
        const labels = this.weeklyData.map(item => item.weekLabel);
        const billableData = this.weeklyData.map(item => item.billableHours);
        const nonBillableData = this.weeklyData.map(item => item.nonBillableHours);
        
        // Create chart configuration
        this.weeklyChartConfig = {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Billable Hours',
                        data: billableData,
                        backgroundColor: 'rgba(54, 162, 235, 0.6)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Non-Billable Hours',
                        data: nonBillableData,
                        backgroundColor: 'rgba(255, 99, 132, 0.6)',
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    xAxes: [{
                        stacked: true
                    }],
                    yAxes: [{
                        stacked: true,
                        ticks: {
                            beginAtZero: true
                        }
                    }]
                },
                title: {
                    display: true,
                    text: 'Weekly Hours'
                }
            }
        };
        
        // Create chart
        if (this.weeklyChart) {
            this.weeklyChart.destroy();
        }
        this.weeklyChart = new Chart(canvas, this.weeklyChartConfig);
    }
}
