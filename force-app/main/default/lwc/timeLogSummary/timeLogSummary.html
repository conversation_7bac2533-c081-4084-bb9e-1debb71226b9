<template>
    <lightning-card title={title} icon-name="standard:timesheet_entry">
        <div class="slds-var-p-horizontal_small slds-var-p-horizontal_small">
            <template if:true={isLoading}>
                <lightning-spinner alternative-text="Loading" size="small"></lightning-spinner>
            </template>
            
            <template if:true={error}>
                <div class="slds-text-color_error slds-var-p-around_small slds-var-p-around_small">
                    Error loading time logs: {error.message}
                </div>
            </template>
            
            <template if:false={isLoading}>
                <template if:true={timeLogs.length}>
                    <ul class="slds-has-dividers_bottom-space">
                        <template for:each={timeLogs} for:item="log">
                            <li key={log.Id} class="slds-item slds-var-p-vertical_x-small slds-var-p-vertical_x-small">
                                <div class="slds-grid slds-gutters">
                                    <div class="slds-col slds-size_1-of-12">
                                        <lightning-icon icon-name={log.objectTypeIcon} size="small"></lightning-icon>
                                    </div>
                                    <div class="slds-col slds-size_11-of-12">
                                        <div class="slds-grid slds-wrap">
                                            <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2">
                                                <a onclick={navigateToRecord} data-id={log.Id} class="slds-text-link_reset">
                                                    <div class="slds-text-heading_small">{log.Description__c}</div>
                                                </a>
                                            </div>
                                            <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-2 slds-text-align_right">
                                                <template if:true={log.Duration_Hours__c}>
                                                    <span class="slds-badge slds-badge_lightest">
                                                        {log.Duration_Hours__c} hours
                                                    </span>
                                                </template>
                                                <template if:true={log.Billable__c}>
                                                    <span class="slds-badge slds-theme_success slds-var-m-left_x-small slds-var-m-left_x-small" title="Success">
                                                        Billable
                                                    </span>
                                                </template>
                                            </div>
                                            <div class="slds-col slds-size_1-of-1 slds-text-body_small slds-text-color_weak">
                                                <div class="slds-grid slds-gutters">
                                                    <div class="slds-col slds-var-p-left_x-small slds-var-p-left_x-small">
                                                        <lightning-formatted-text value={log.Object_Type__c}></lightning-formatted-text>
                                                        <template if:true={log.Related_Object__c}>
                                                            : {log.Related_Object__c}
                                                        </template>
                                                    </div>
                                                    <div class="slds-col slds-text-align_right slds-var-m-right_x-small slds-var-m-right_x-small">
                                                        {log.formattedStartTime}
                                                        <template if:true={log.formattedEndTime}>
                                                            - {log.formattedEndTime}
                                                        </template>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </template>
                    </ul>
                </template>
                <template if:false={timeLogs.length}>
                    <div class="slds-text-align_center slds-var-p-around_medium slds-text-color_weak slds-var-p-around_medium">
                        No time logs found.
                    </div>
                </template>
            </template>
        </div>
        <template if:true={showRefreshButton}>
            <lightning-button-icon 
                slot="actions"
                icon-name="utility:refresh" 
                alternative-text="Refresh" 
                title="Refresh" 
                onclick={handleRefresh}
                aria-label="Refresh button">
            </lightning-button-icon>
        </template>
        <div class="slds-var-p-vertical_x-small slds-var-p-vertical_x-small">
            <div class="slds-var-m-left_x-small slds-var-m-left_x-small">
                <div class="slds-var-m-bottom_x-small slds-var-m-bottom_x-small">
                    <div class="slds-var-m-top_x-small slds-var-m-top_x-small">
                        <div class="slds-var-m-top_small slds-var-m-top_small">
                            <div class="slds-var-m-top_medium slds-var-m-top_medium">
                                <!-- Accessibility labels for clickable images, buttons, and form elements -->
                                <lightning-button icon-name="utility:refresh" alternative-text="Refresh" aria-label="Refresh button"></lightning-button>
                                <img src="/path/to/image" alt="Descriptive text" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="slds-var-m-left_x-small slds-var-m-left_x-small">
            <div class="slds-var-m-bottom_x-small slds-var-m-bottom_x-small">
                <div class="slds-var-m-top_x-small slds-var-m-top_x-small">
                    <div class="slds-var-m-top_small slds-var-m-top_small">
                        <div class="slds-var-m-top_medium slds-var-m-top_medium">
                            <!-- Accessibility labels for clickable images, buttons, and form elements -->
                            <lightning-button icon-name="utility:refresh" alternative-text="Refresh" aria-label="Refresh button"></lightning-button>
                            <img src="/path/to/image" alt="Descriptive text" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </lightning-card>
    <lightning-button 
        label="View All Time Logs" 
        variant="base" 
        onclick={navigateToTimeLogTab} 
        class="slds-var-m-left_x-small slds-var-m-left_x-small"
        slot="footer">
    </lightning-button>
</template>
