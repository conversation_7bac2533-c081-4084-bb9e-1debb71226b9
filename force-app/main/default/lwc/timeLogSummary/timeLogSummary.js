import { LightningElement, wire, api } from 'lwc';
import { refreshApex } from '@salesforce/apex';
import getRecentTimeLogs from '@salesforce/apex/TimeLoggerController.getRecentTimeLogs';
import { NavigationMixin } from 'lightning/navigation';

export default class TimeLogSummary extends NavigationMixin(LightningElement) {
    @api recordsToDisplay = 5;
    @api showRefreshButton = false;
    @api title = 'Recent Time Logs';

    timeLogs = [];
    wiredTimeLogsResult;
    isLoading = true;
    error;

    // Wire the getRecentTimeLogs method from the Apex controller
    @wire(getRecentTimeLogs, { limitCount: '$recordsToDisplay' })
    wiredTimeLogs(result) {
        this.wiredTimeLogsResult = result;
        this.isLoading = true;

        if (result.data) {
            this.timeLogs = result.data.map(log => {
                return {
                    ...log,
                    formattedStartTime: this.formatDateTime(log.Start_Time__c),
                    formattedEndTime: this.formatDateTime(log.End_Time__c),
                    objectTypeIcon: this.getObjectIcon(log.Object_Type__c)
                };
            });
            this.error = undefined;
            this.isLoading = false;
        } else if (result.error) {
            this.error = result.error;
            this.timeLogs = [];
            this.isLoading = false;
        }
    }

    // Format date time values
    formatDateTime(dateTimeValue) {
        if (!dateTimeValue) return '';

        const date = new Date(dateTimeValue);
        return new Intl.DateTimeFormat('en-US', {
            month: 'short',
            day: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        }).format(date);
    }

    // Get icon based on object type
    getObjectIcon(objectType) {
        switch(objectType) {
            case 'Case':
                return 'standard:case';
            case 'Account':
                return 'standard:account';
            case 'Opportunity':
                return 'standard:opportunity';
            case 'Contact':
                return 'standard:contact';
            case 'Custom Project':
                return 'standard:custom';
            default:
                return 'standard:default';
        }
    }

    // Handle refresh button click
    handleRefresh() {
        this.isLoading = true;
        refreshApex(this.wiredTimeLogsResult)
            .finally(() => {
                this.isLoading = false;
            });
    }

    // Navigate to Time Log record
    navigateToRecord(event) {
        const recordId = event.currentTarget.dataset.id;
        this[NavigationMixin.Navigate]({
            type: 'standard__recordPage',
            attributes: {
                recordId: recordId,
                objectApiName: 'Time_Log__c',
                actionName: 'view'
            }
        });
    }

    // Navigate to Time Log tab
    navigateToTimeLogTab() {
        this[NavigationMixin.Navigate]({
            type: 'standard__objectPage',
            attributes: {
                objectApiName: 'Time_Log__c',
                actionName: 'list'
            }
        });
    }
}
