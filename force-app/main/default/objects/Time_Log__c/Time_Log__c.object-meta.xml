<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <label>Time Log</label>
    <pluralLabel>Time Logs</pluralLabel>
    <nameField>
        <type>AutoNumber</type>
        <label>Time Log Name</label>
        <displayFormat>TL-{00000}</displayFormat>
    </nameField>
    <deploymentStatus>Deployed</deploymentStatus>
    <sharingModel>ReadWrite</sharingModel>
    <fields>
        <fullName>User__c</fullName>
        <label>User</label>
        <type>Lookup</type>
        <referenceTo>User</referenceTo>
        <required>false</required>
    </fields>
    <fields>
        <fullName>Related_Object__c</fullName>
        <label>Related Record</label>
        <type>Text</type>
        <length>255</length>
        <required>false</required>
        <description>Polymorphic lookup not natively supported; use text or create specific lookups as needed.</description>
    </fields>
    <fields>
        <fullName>Object_Type__c</fullName>
        <label>Object Type</label>
        <type>Picklist</type>
        <valueSet>
            <valueSetDefinition>
                <sorted>false</sorted>
                <value><fullName>Account</fullName><default>false</default></value>
                <value><fullName>Opportunity</fullName><default>false</default></value>
                <value><fullName>Case</fullName><default>false</default></value>
                <value><fullName>Contact</fullName><default>false</default></value>
                <value><fullName>Custom Project</fullName><default>false</default></value>
                <value><fullName>Other</fullName><default>false</default></value>
            </valueSetDefinition>
        </valueSet>
        <required>false</required>
    </fields>
    <fields>
        <fullName>Start_Time__c</fullName>
        <label>Start Time</label>
        <type>DateTime</type>
        <required>false</required>
    </fields>
    <fields>
        <fullName>End_Time__c</fullName>
        <label>End Time</label>
        <type>DateTime</type>
        <required>false</required>
    </fields>
    <fields>
        <fullName>Duration_Hours__c</fullName>
        <label>Duration (Hours)</label>
        <type>Number</type>
        <precision>6</precision>
        <scale>2</scale>
        <required>false</required>
        <formula>((End_Time__c - Start_Time__c) * 24)</formula>
        <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
    </fields>
    <fields>
        <fullName>Description__c</fullName>
        <label>Description</label>
        <type>LongTextArea</type>
        <length>32768</length>
        <visibleLines>3</visibleLines>
        <required>false</required>
    </fields>
    <fields>
        <fullName>Billable__c</fullName>
        <label>Billable?</label>
        <type>Checkbox</type>
        <defaultValue>false</defaultValue>
        <required>false</required>
    </fields>
    <fields>
        <fullName>Opportunity__c</fullName>
        <label>Opportunity</label>
        <type>Lookup</type>
        <referenceTo>Opportunity</referenceTo>
        <required>false</required>
    </fields>
    <fields>
        <fullName>Case__c</fullName>
        <label>Case</label>
        <type>Lookup</type>
        <referenceTo>Case</referenceTo>
        <required>false</required>
    </fields>
    <fields>
        <fullName>Account__c</fullName>
        <label>Account</label>
        <type>Lookup</type>
        <referenceTo>Account</referenceTo>
        <required>false</required>
    </fields>
    <fields>
        <fullName>Contact__c</fullName>
        <label>Contact</label>
        <type>Lookup</type>
        <referenceTo>Contact</referenceTo>
        <required>false</required>
    </fields>
    <fields>
        <fullName>Assessment__c</fullName>
        <label>Assessment</label>
        <type>Lookup</type>
        <referenceTo>Assessment__c</referenceTo>
        <required>false</required>
    </fields>
    <fields>
        <fullName>Campaign__c</fullName>
        <label>Campaign</label>
        <type>Lookup</type>
        <referenceTo>Campaign</referenceTo>
        <required>false</required>
    </fields>
    <fields>
        <fullName>Case_Activity__c</fullName>
        <label>Case Activity</label>
        <type>Lookup</type>
        <referenceTo>Case_Activity__c</referenceTo>
        <required>false</required>
    </fields>

    <fields>
        <fullName>Potential__c</fullName>
        <label>Potential</label>
        <type>Lookup</type>
        <referenceTo>Potential__c</referenceTo>
        <required>false</required>
    </fields>
    <fields>
        <fullName>Recognition__c</fullName>
        <label>Recognition</label>
        <type>Lookup</type>
        <referenceTo>Recognition__c</referenceTo>
        <required>false</required>
    </fields>
</CustomObject>
