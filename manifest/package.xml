<?xml version="1.0" encoding="UTF-8"?>
<Package xmlns="http://soap.sforce.com/2006/04/metadata">
    <types>
        <members>Time_Log__c</members>
        <name>CustomObject</name>
    </types>
    <types>
        <members>Time_Log__c</members>
        <name>CustomTab</name>
    </types>
    <types>
        <members>Time_Log__c.Object_Type__c</members>
        <members>Time_Log__c.Start_Time__c</members>
        <members>Time_Log__c.End_Time__c</members>
        <members>Time_Log__c.Duration_Hours__c</members>
        <members>Time_Log__c.Description__c</members>
        <members>Time_Log__c.Billable__c</members>
        <members>Time_Log__c.User__c</members>
        <members>Time_Log__c.Related_Object__c</members>
        <members>Time_Log__c.Account__c</members>
        <members>Time_Log__c.Opportunity__c</members>
        <members>Time_Log__c.Case__c</members>
        <members>Time_Log__c.Contact__c</members>
        <name>CustomField</name>
    </types>
    <types>
        <members>timeLogForm</members>
        <members>timeLogStats</members>
        <members>timeLogSummary</members>
        <members>employeeTimeLogDashboard</members>
        <name>LightningComponentBundle</name>
    </types>
    <types>
        <members>TimeLoggerController</members>
        <members>TimeLoggerService</members>
        <members>TimeLoggerControllerTest</members>
        <name>ApexClass</name>
    </types>
    <types>
        <members>chartjs</members>
        <name>StaticResource</name>
    </types>
    <types>
        <members>Time_Log_Viewer</members>
        <members>Employee_Time_Log_Dashboard</members>
        <name>FlexiPage</name>
    </types>
    <types>
        <members>Time_Log_Dashboards</members>
        <name>DashboardFolder</name>
    </types>
    <types>
        <members>Time_Log_Dashboards/Time_Tracking_Dashboard</members>
        <name>Dashboard</name>
    </types>
    <types>
        <members>Time_Log_Reports</members>
        <name>ReportFolder</name>
    </types>
    <types>
        <members>Time_Log_Reports/Time_by_User</members>
        <members>Time_Log_Reports/Time_by_Object_Type</members>
        <members>Time_Log_Reports/Billable_vs_NonBillable_Time</members>
        <name>Report</name>
    </types>
    <types>
        <members>Employee_Time_Log_Dashboard</members>
        <n>CustomTab</n>
    </types>
    <types>
        <members>WEAREALX_Central</members>
        <n>CustomApplication</n>
    </types>
    <types>
        <members>WEAREALX_Central_UtilityBar</members>
        <n>FlexiPage</n>
    </types>
    <version>62.0</version>
</Package>
