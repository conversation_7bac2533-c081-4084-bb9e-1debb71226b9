// Check chart data for a user
Id userId = UserInfo.getUserId();
System.debug('Checking chart data for user: ' + userId);

// Get user stats which should include chart data
Map<String, Object> stats = TimeLoggerService.getUserTimeLogStats(userId);

// Check total hours and billable hours
System.debug('Total Hours: ' + stats.get('totalHours'));
System.debug('Billable Hours: ' + stats.get('billableHours'));

// Check object type data
List<Map<String, Object>> byObjectType = (List<Map<String, Object>>)stats.get('byObjectType');
System.debug('Object Type Data Count: ' + (byObjectType != null ? byObjectType.size() : 0));

if (byObjectType != null && !byObjectType.isEmpty()) {
    for (Map<String, Object> item : byObjectType) {
        System.debug('  Object Type: ' + item.get('objectType') + ', Hours: ' + item.get('hours'));
    }
}

// Check monthly data
List<Map<String, Object>> byMonth = (List<Map<String, Object>>)stats.get('byMonth');
System.debug('Monthly Data Count: ' + (byMonth != null ? byMonth.size() : 0));

if (byMonth != null && !byMonth.isEmpty()) {
    for (Map<String, Object> item : byMonth) {
        System.debug('  Month: ' + item.get('monthLabel') + ', Hours: ' + item.get('hours'));
    }
}
