// Create a test time log for the current user with a past date
Id userId = UserInfo.getUserId();
System.debug('Creating past time log for user: ' + userId);

// Create a test account if needed
List<Account> testAccounts = [SELECT Id FROM Account WHERE Name = 'Test Account' LIMIT 1];
Id accountId;

if (testAccounts.isEmpty()) {
    Account testAccount = new Account(Name = 'Test Account');
    insert testAccount;
    accountId = testAccount.Id;
    System.debug('Created test account: ' + accountId);
} else {
    accountId = testAccounts[0].Id;
    System.debug('Using existing test account: ' + accountId);
}

// Create a time log for last month
Date lastMonth = Date.today().addMonths(-1);
Datetime startTime = Datetime.newInstance(lastMonth.year(), lastMonth.month(), 15, 10, 0, 0);
Datetime endTime = startTime.addHours(2);

Time_Log__c timeLog = new Time_Log__c(
    Object_Type__c = 'Account',
    Related_Object__c = accountId,
    Description__c = 'Past time log created via script',
    Start_Time__c = startTime,
    End_Time__c = endTime,
    User__c = userId,
    Billable__c = true
);

insert timeLog;
System.debug('Created past time log: ' + timeLog.Id);

// Query to verify
List<Time_Log__c> logs = [
    SELECT Id, Object_Type__c, Related_Object__c, Start_Time__c, End_Time__c, 
           Duration_Hours__c, Description__c, Billable__c, User__c
    FROM Time_Log__c 
    WHERE Id = :timeLog.Id
];

System.debug('Verification query result: ' + logs.size() + ' records found');
if (!logs.isEmpty()) {
    System.debug('Time log details: ' + logs[0]);
}
