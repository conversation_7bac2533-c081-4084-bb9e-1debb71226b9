// Create a test time log for the current user
Id userId = UserInfo.getUserId();
System.debug('Creating test time log for user: ' + userId);

// Create a test account if needed
List<Account> testAccounts = [SELECT Id FROM Account WHERE Name = 'Test Account' LIMIT 1];
Id accountId;

if (testAccounts.isEmpty()) {
    Account testAccount = new Account(Name = 'Test Account');
    insert testAccount;
    accountId = testAccount.Id;
    System.debug('Created test account: ' + accountId);
} else {
    accountId = testAccounts[0].Id;
    System.debug('Using existing test account: ' + accountId);
}

// Create a time log for today
Time_Log__c timeLog = new Time_Log__c(
    Object_Type__c = 'Account',
    Related_Object__c = accountId,
    Description__c = 'Test time log created via script',
    Start_Time__c = Datetime.now().addHours(-2),
    End_Time__c = Datetime.now().addHours(-1),
    User__c = userId,
    Billable__c = true
);

insert timeLog;
System.debug('Created time log: ' + timeLog.Id);

// Query to verify
List<Time_Log__c> logs = [
    SELECT Id, Object_Type__c, Related_Object__c, Start_Time__c, End_Time__c, 
           Duration_Hours__c, Description__c, Billable__c, User__c
    FROM Time_Log__c 
    WHERE Id = :timeLog.Id
];

System.debug('Verification query result: ' + logs.size() + ' records found');
if (!logs.isEmpty()) {
    System.debug('Time log details: ' + logs[0]);
}
