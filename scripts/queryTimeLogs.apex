// Query all time logs for the current user
Id userId = UserInfo.getUserId();
System.debug('Querying time logs for user: ' + userId);

List<Time_Log__c> allLogs = [
    SELECT Id, Object_Type__c, Related_Object__c, Start_Time__c, End_Time__c, 
           Duration_Hours__c, Description__c, Billable__c, User__c
    FROM Time_Log__c 
    WHERE User__c = :userId
    ORDER BY Start_Time__c DESC
];

System.debug('Found ' + allLogs.size() + ' time logs');

// Display details of each time log
for (Time_Log__c log : allLogs) {
    System.debug('Time Log: ' + log.Id);
    System.debug('  Object Type: ' + log.Object_Type__c);
    System.debug('  Start Time: ' + log.Start_Time__c);
    System.debug('  End Time: ' + log.End_Time__c);
    System.debug('  Duration: ' + log.Duration_Hours__c);
    System.debug('  Description: ' + log.Description__c);
    System.debug('  Billable: ' + log.Billable__c);
}

// Now let's check the getUserTimeLogs method with THIS_MONTH filter
System.debug('Testing getUserTimeLogs with THIS_MONTH filter');
List<Time_Log__c> thisMonthLogs = TimeLoggerService.getUserTimeLogs(
    userId, 
    'THIS_MONTH', 
    null, 
    null, 
    100
);
System.debug('THIS_MONTH filter returned ' + thisMonthLogs.size() + ' logs');

// Test with LAST_MONTH filter
System.debug('Testing getUserTimeLogs with LAST_MONTH filter');
List<Time_Log__c> lastMonthLogs = TimeLoggerService.getUserTimeLogs(
    userId, 
    'LAST_MONTH', 
    null, 
    null, 
    100
);
System.debug('LAST_MONTH filter returned ' + lastMonthLogs.size() + ' logs');

// Test with ALL filter
System.debug('Testing getUserTimeLogs with ALL filter');
List<Time_Log__c> allTimeLogs = TimeLoggerService.getUserTimeLogs(
    userId, 
    'ALL', 
    null, 
    null, 
    100
);
System.debug('ALL filter returned ' + allTimeLogs.size() + ' logs');
