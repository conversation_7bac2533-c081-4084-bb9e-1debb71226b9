// Get the time log record
Time_Log__c timeLog = [
    SELECT Id, Object_Type__c, Related_Object__c, User__c
    FROM Time_Log__c
    WHERE Object_Type__c = 'Opportunity'
    LIMIT 1
];

// Get the current user ID
Id userId = UserInfo.getUserId();

// Update the time log with the current user ID
timeLog.User__c = userId;

// Update the record
update timeLog;

// Verify the update
Time_Log__c updatedLog = [
    SELECT Id, Object_Type__c, Related_Object__c, User__c
    FROM Time_Log__c
    WHERE Id = :timeLog.Id
];

System.debug('Updated time log: ' + updatedLog);
